{"version": 3, "sources": ["../../../src/lib/eslint/customFormatter.ts"], "sourcesContent": ["import { bold, cyan, gray, red, yellow } from '../picocolors'\nimport path from 'path'\n\n// eslint-disable-next-line no-shadow\nexport enum MessageSeverity {\n  Warning = 1,\n  Error = 2,\n}\n\ninterface LintMessage {\n  ruleId: string | null\n  severity: 1 | 2\n  message: string\n  line: number\n  column: number\n}\n\nexport interface LintResult {\n  filePath: string\n  messages: LintMessage[]\n  errorCount: number\n  warningCount: number\n  output?: string\n  source?: string\n}\n\nfunction pluginCount(messages: LintMessage[]): {\n  nextPluginErrorCount: number\n  nextPluginWarningCount: number\n} {\n  let nextPluginWarningCount = 0\n  let nextPluginErrorCount = 0\n\n  for (let i = 0; i < messages.length; i++) {\n    const { severity, ruleId } = messages[i]\n\n    if (ruleId?.includes('@next/next')) {\n      if (severity === MessageSeverity.Warning) {\n        nextPluginWarningCount += 1\n      } else {\n        nextPluginErrorCount += 1\n      }\n    }\n  }\n\n  return {\n    nextPluginErrorCount,\n    nextPluginWarningCount,\n  }\n}\n\nfunction formatMessage(\n  dir: string,\n  messages: LintMessage[],\n  filePath: string\n): string {\n  let fileName = path.posix.normalize(\n    path.relative(dir, filePath).replace(/\\\\/g, '/')\n  )\n\n  if (!fileName.startsWith('.')) {\n    fileName = './' + fileName\n  }\n\n  let output = '\\n' + cyan(fileName)\n\n  for (let i = 0; i < messages.length; i++) {\n    const { message, severity, line, column, ruleId } = messages[i]\n\n    output = output + '\\n'\n\n    if (line && column) {\n      output =\n        output +\n        yellow(line.toString()) +\n        ':' +\n        yellow(column.toString()) +\n        '  '\n    }\n\n    if (severity === MessageSeverity.Warning) {\n      output += yellow(bold('Warning')) + ': '\n    } else {\n      output += red(bold('Error')) + ': '\n    }\n\n    output += message\n\n    if (ruleId) {\n      output += '  ' + gray(bold(ruleId))\n    }\n  }\n\n  return output\n}\n\nexport async function formatResults(\n  baseDir: string,\n  results: LintResult[],\n  format: (r: LintResult[]) => string | Promise<string>\n): Promise<{\n  output: string\n  outputWithMessages: string\n  totalNextPluginErrorCount: number\n  totalNextPluginWarningCount: number\n}> {\n  let totalNextPluginErrorCount = 0\n  let totalNextPluginWarningCount = 0\n  let resultsWithMessages = results.filter(({ messages }) => messages?.length)\n\n  // Track number of Next.js plugin errors and warnings\n  resultsWithMessages.forEach(({ messages }) => {\n    const res = pluginCount(messages)\n    totalNextPluginErrorCount += res.nextPluginErrorCount\n    totalNextPluginWarningCount += res.nextPluginWarningCount\n  })\n\n  // Use user defined formatter or Next.js's built-in custom formatter\n  const output = format\n    ? await format(resultsWithMessages)\n    : resultsWithMessages\n        .map(({ messages, filePath }) =>\n          formatMessage(baseDir, messages, filePath)\n        )\n        .join('\\n')\n\n  return {\n    output: output,\n    outputWithMessages:\n      resultsWithMessages.length > 0\n        ? output +\n          `\\n\\n${cyan(\n            'info'\n          )}  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules`\n        : '',\n    totalNextPluginErrorCount,\n    totalNextPluginWarningCount,\n  }\n}\n"], "names": ["MessageSeverity", "formatResults", "pluginCount", "messages", "nextPluginWarningCount", "nextPluginErrorCount", "i", "length", "severity", "ruleId", "includes", "formatMessage", "dir", "filePath", "fileName", "path", "posix", "normalize", "relative", "replace", "startsWith", "output", "cyan", "message", "line", "column", "yellow", "toString", "bold", "red", "gray", "baseDir", "results", "format", "totalNextPluginErrorCount", "totalNextPluginWarningCount", "resultsWithMessages", "filter", "for<PERSON>ach", "res", "map", "join", "outputWithMessages"], "mappings": ";;;;;;;;;;;;;;;IAIYA,eAAe;eAAfA;;IA4FUC,aAAa;eAAbA;;;4BAhGwB;6DAC7B;;;;;;AAGV,IAAA,AAAKD,yCAAAA;;;WAAAA;;AAsBZ,SAASE,YAAYC,QAAuB;IAI1C,IAAIC,yBAAyB;IAC7B,IAAIC,uBAAuB;IAE3B,IAAK,IAAIC,IAAI,GAAGA,IAAIH,SAASI,MAAM,EAAED,IAAK;QACxC,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGN,QAAQ,CAACG,EAAE;QAExC,IAAIG,0BAAAA,OAAQC,QAAQ,CAAC,eAAe;YAClC,IAAIF,gBAAsC;gBACxCJ,0BAA0B;YAC5B,OAAO;gBACLC,wBAAwB;YAC1B;QACF;IACF;IAEA,OAAO;QACLA;QACAD;IACF;AACF;AAEA,SAASO,cACPC,GAAW,EACXT,QAAuB,EACvBU,QAAgB;IAEhB,IAAIC,WAAWC,aAAI,CAACC,KAAK,CAACC,SAAS,CACjCF,aAAI,CAACG,QAAQ,CAACN,KAAKC,UAAUM,OAAO,CAAC,OAAO;IAG9C,IAAI,CAACL,SAASM,UAAU,CAAC,MAAM;QAC7BN,WAAW,OAAOA;IACpB;IAEA,IAAIO,SAAS,OAAOC,IAAAA,gBAAI,EAACR;IAEzB,IAAK,IAAIR,IAAI,GAAGA,IAAIH,SAASI,MAAM,EAAED,IAAK;QACxC,MAAM,EAAEiB,OAAO,EAAEf,QAAQ,EAAEgB,IAAI,EAAEC,MAAM,EAAEhB,MAAM,EAAE,GAAGN,QAAQ,CAACG,EAAE;QAE/De,SAASA,SAAS;QAElB,IAAIG,QAAQC,QAAQ;YAClBJ,SACEA,SACAK,IAAAA,kBAAM,EAACF,KAAKG,QAAQ,MACpB,MACAD,IAAAA,kBAAM,EAACD,OAAOE,QAAQ,MACtB;QACJ;QAEA,IAAInB,gBAAsC;YACxCa,UAAUK,IAAAA,kBAAM,EAACE,IAAAA,gBAAI,EAAC,cAAc;QACtC,OAAO;YACLP,UAAUQ,IAAAA,eAAG,EAACD,IAAAA,gBAAI,EAAC,YAAY;QACjC;QAEAP,UAAUE;QAEV,IAAId,QAAQ;YACVY,UAAU,OAAOS,IAAAA,gBAAI,EAACF,IAAAA,gBAAI,EAACnB;QAC7B;IACF;IAEA,OAAOY;AACT;AAEO,eAAepB,cACpB8B,OAAe,EACfC,OAAqB,EACrBC,MAAqD;IAOrD,IAAIC,4BAA4B;IAChC,IAAIC,8BAA8B;IAClC,IAAIC,sBAAsBJ,QAAQK,MAAM,CAAC,CAAC,EAAElC,QAAQ,EAAE,GAAKA,4BAAAA,SAAUI,MAAM;IAE3E,qDAAqD;IACrD6B,oBAAoBE,OAAO,CAAC,CAAC,EAAEnC,QAAQ,EAAE;QACvC,MAAMoC,MAAMrC,YAAYC;QACxB+B,6BAA6BK,IAAIlC,oBAAoB;QACrD8B,+BAA+BI,IAAInC,sBAAsB;IAC3D;IAEA,oEAAoE;IACpE,MAAMiB,SAASY,SACX,MAAMA,OAAOG,uBACbA,oBACGI,GAAG,CAAC,CAAC,EAAErC,QAAQ,EAAEU,QAAQ,EAAE,GAC1BF,cAAcoB,SAAS5B,UAAUU,WAElC4B,IAAI,CAAC;IAEZ,OAAO;QACLpB,QAAQA;QACRqB,oBACEN,oBAAoB7B,MAAM,GAAG,IACzBc,SACA,CAAC,IAAI,EAAEC,IAAAA,gBAAI,EACT,QACA,+HAA+H,CAAC,GAClI;QACNY;QACAC;IACF;AACF"}