{"version": 3, "sources": ["../../../src/server/app-render/create-component-styles-and-scripts.tsx"], "sourcesContent": ["import React from 'react'\nimport { interopDefault } from './interop-default'\nimport { getLinkAndScriptTags } from './get-css-inlined-link-tags'\nimport type { AppRenderContext } from './app-render'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { encodeURIPath } from '../../shared/lib/encode-uri-path'\nimport { renderCssResource } from './render-css-resource'\n\nexport async function createComponentStylesAndScripts({\n  filePath,\n  getComponent,\n  injectedCSS,\n  injectedJS,\n  ctx,\n}: {\n  filePath: string\n  getComponent: () => any\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  ctx: AppRenderContext\n}): Promise<[React.ComponentType<any>, React.ReactNode, React.ReactNode]> {\n  const { styles: entryCssFiles, scripts: jsHrefs } = getLinkAndScriptTags(\n    ctx.clientReferenceManifest,\n    filePath,\n    injectedCSS,\n    injectedJS\n  )\n\n  const styles = renderCssResource(entryCssFiles, ctx)\n\n  const scripts = jsHrefs\n    ? jsHrefs.map((href, index) => (\n        <script\n          src={`${ctx.assetPrefix}/_next/${encodeURIPath(\n            href\n          )}${getAssetQueryString(ctx, true)}`}\n          async={true}\n          key={`script-${index}`}\n        />\n      ))\n    : null\n\n  const Comp = interopDefault(await getComponent())\n\n  return [Comp, styles, scripts]\n}\n"], "names": ["React", "interopDefault", "getLinkAndScriptTags", "getAssetQueryString", "encodeURIPath", "renderCssResource", "createComponentStylesAndScripts", "filePath", "getComponent", "injectedCSS", "injectedJS", "ctx", "styles", "entryCssFiles", "scripts", "jsHrefs", "clientReferenceManifest", "map", "href", "index", "script", "src", "assetPrefix", "async", "Comp"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,oBAAoB,QAAQ,8BAA6B;AAElE,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,aAAa,QAAQ,mCAAkC;AAChE,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD,OAAO,eAAeC,gCAAgC,EACpDC,QAAQ,EACRC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,GAAG,EAOJ;IACC,MAAM,EAAEC,QAAQC,aAAa,EAAEC,SAASC,OAAO,EAAE,GAAGb,qBAClDS,IAAIK,uBAAuB,EAC3BT,UACAE,aACAC;IAGF,MAAME,SAASP,kBAAkBQ,eAAeF;IAEhD,MAAMG,UAAUC,UACZA,QAAQE,GAAG,CAAC,CAACC,MAAMC,sBACjB,KAACC;YACCC,KAAK,GAAGV,IAAIW,WAAW,CAAC,OAAO,EAAElB,cAC/Bc,QACEf,oBAAoBQ,KAAK,OAAO;YACpCY,OAAO;WACF,CAAC,OAAO,EAAEJ,OAAO,KAG1B;IAEJ,MAAMK,OAAOvB,eAAe,MAAMO;IAElC,OAAO;QAACgB;QAAMZ;QAAQE;KAAQ;AAChC"}