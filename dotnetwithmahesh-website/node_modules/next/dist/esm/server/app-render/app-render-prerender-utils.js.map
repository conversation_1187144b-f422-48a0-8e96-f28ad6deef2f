{"version": 3, "sources": ["../../../src/server/app-render/app-render-prerender-utils.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\nimport { isPrerenderInterruptedError } from './dynamic-rendering'\n\n/**\n * This is a utility function to make scheduling sequential tasks that run back to back easier.\n * We schedule on the same queue (setImmediate) at the same time to ensure no other events can sneak in between.\n */\nexport function prerenderAndAbortInSequentialTasks<R>(\n  prerender: () => Promise<R>,\n  abort: () => void\n): Promise<R> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`prerenderAndAbortInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let pendingResult: Promise<R>\n      setImmediate(() => {\n        try {\n          pendingResult = prerender()\n          pendingResult.catch(() => {})\n        } catch (err) {\n          reject(err)\n        }\n      })\n      setImmediate(() => {\n        abort()\n        resolve(pendingResult)\n      })\n    })\n  }\n}\n\nexport function prerenderServerWithPhases(\n  signal: AbortSignal,\n  render: () => ReadableStream<Uint8Array>,\n  finalPhase: () => void\n): Promise<ServerPrerenderStreamResult>\nexport function prerenderServerWithPhases(\n  signal: AbortSignal,\n  render: () => ReadableStream<Uint8Array>,\n  secondPhase: () => void,\n  finalPhase: () => void\n): Promise<ServerPrerenderStreamResult>\nexport function prerenderServerWithPhases(\n  signal: AbortSignal,\n  render: () => ReadableStream<Uint8Array>,\n  secondPhase: () => void,\n  thirdPhase: () => void,\n  ...remainingPhases: Array<() => void>\n): Promise<ServerPrerenderStreamResult>\nexport function prerenderServerWithPhases(\n  signal: AbortSignal,\n  render: () => ReadableStream<Uint8Array>,\n  ...remainingPhases: Array<() => void>\n): Promise<ServerPrerenderStreamResult> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`prerenderAndAbortInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let result: ServerPrerenderStreamResult\n\n      signal.addEventListener(\n        'abort',\n        () => {\n          if (isPrerenderInterruptedError(signal.reason)) {\n            result.markInterrupted()\n          } else {\n            result.markComplete()\n          }\n        },\n        {\n          once: true,\n        }\n      )\n\n      setImmediate(() => {\n        try {\n          result = new ServerPrerenderStreamResult(render())\n        } catch (err) {\n          reject(err)\n        }\n      })\n\n      function runFinalTask(this: () => void) {\n        try {\n          if (result) {\n            result.markComplete()\n            this()\n          }\n          resolve(result)\n        } catch (err) {\n          reject(err)\n        }\n      }\n\n      function runNextTask(this: () => void) {\n        try {\n          if (result) {\n            result.markPhase()\n            this()\n          }\n        } catch (err) {\n          reject(err)\n        }\n      }\n\n      let i = 0\n      for (; i < remainingPhases.length - 1; i++) {\n        const phase = remainingPhases[i]\n        setImmediate(runNextTask.bind(phase))\n      }\n      if (remainingPhases[i]) {\n        const finalPhase = remainingPhases[i]\n        setImmediate(runFinalTask.bind(finalPhase))\n      }\n    })\n  }\n}\n\nconst PENDING = 0\nconst COMPLETE = 1\nconst INTERRUPTED = 2\nconst ERRORED = 3\n\nexport class ServerPrerenderStreamResult {\n  private currentChunks: Array<Uint8Array>\n  private chunksByPhase: Array<Array<Uint8Array>>\n  private trailingChunks: Array<Uint8Array>\n  private status: 0 | 1 | 2 | 3\n  private reason: null | unknown\n\n  constructor(stream: ReadableStream<Uint8Array>) {\n    this.status = PENDING\n    this.reason = null\n\n    this.trailingChunks = []\n    this.currentChunks = []\n    this.chunksByPhase = [this.currentChunks]\n\n    const reader = stream.getReader()\n\n    const progress = ({\n      done,\n      value,\n    }: ReadableStreamReadResult<Uint8Array>) => {\n      if (done) {\n        if (this.status === PENDING) {\n          this.status = COMPLETE\n        }\n        return\n      }\n      if (this.status === PENDING || this.status === INTERRUPTED) {\n        this.currentChunks.push(value)\n      } else {\n        this.trailingChunks.push(value)\n      }\n      reader.read().then(progress, error)\n    }\n    const error = (reason: unknown) => {\n      this.status = ERRORED\n      this.reason = reason\n    }\n\n    reader.read().then(progress, error)\n  }\n\n  markPhase() {\n    this.currentChunks = []\n    this.chunksByPhase.push(this.currentChunks)\n  }\n\n  markComplete() {\n    if (this.status === PENDING) {\n      this.status = COMPLETE\n    }\n  }\n\n  markInterrupted() {\n    this.status = INTERRUPTED\n  }\n\n  /**\n   * Returns a stream which only releases chunks when `releasePhase` is called. This stream will never \"complete\" because\n   * we rely upon the stream remaining open when prerendering to avoid triggering errors for incomplete chunks in the client.\n   *\n   * asPhasedStream is expected to be called once per result however it is safe to call multiple times as long as we have not\n   * transferred the underlying data. Generally this will only happen when streaming to a response\n   */\n  asPhasedStream() {\n    switch (this.status) {\n      case COMPLETE:\n      case INTERRUPTED:\n        return new PhasedStream(this.chunksByPhase)\n      default:\n        throw new InvariantError(\n          `ServerPrerenderStreamResult cannot be consumed as a stream because it is not yet complete. status: ${this.status}`\n        )\n    }\n  }\n\n  /**\n   * Returns a stream which will release all chunks immediately. This stream will \"complete\" synchronously. It should be used outside\n   * of render use cases like loading client chunks ahead of SSR or writing the streamed content to disk.\n   */\n  asStream() {\n    switch (this.status) {\n      case COMPLETE:\n      case INTERRUPTED:\n        const chunksByPhase = this.chunksByPhase\n        const trailingChunks = this.trailingChunks\n        return new ReadableStream({\n          start(controller) {\n            for (let i = 0; i < chunksByPhase.length; i++) {\n              const chunks = chunksByPhase[i]\n              for (let j = 0; j < chunks.length; j++) {\n                controller.enqueue(chunks[j])\n              }\n            }\n            for (let i = 0; i < trailingChunks.length; i++) {\n              controller.enqueue(trailingChunks[i])\n            }\n            controller.close()\n          },\n        })\n      default:\n        throw new InvariantError(\n          `ServerPrerenderStreamResult cannot be consumed as a stream because it is not yet complete. status: ${this.status}`\n        )\n    }\n  }\n}\n\nclass PhasedStream<T> extends ReadableStream<T> {\n  private nextPhase: number\n  private chunksByPhase: Array<Array<T>>\n  private destination: ReadableStreamDefaultController<T>\n\n  constructor(chunksByPhase: Array<Array<T>>) {\n    if (chunksByPhase.length === 0) {\n      throw new InvariantError(\n        'PhasedStream expected at least one phase but none were found.'\n      )\n    }\n\n    let destination: ReadableStreamDefaultController<T>\n    super({\n      start(controller) {\n        destination = controller\n      },\n    })\n\n    // the start function above is called synchronously during construction so we will always have a destination\n    // We wait to assign it until after the super call because we cannot access `this` before calling super\n    this.destination = destination!\n    this.nextPhase = 0\n    this.chunksByPhase = chunksByPhase\n    this.releasePhase()\n  }\n\n  releasePhase() {\n    if (this.nextPhase < this.chunksByPhase.length) {\n      const chunks = this.chunksByPhase[this.nextPhase++]\n      for (let i = 0; i < chunks.length; i++) {\n        this.destination.enqueue(chunks[i])\n      }\n    } else {\n      throw new InvariantError(\n        'PhasedStream expected more phases to release but none were found.'\n      )\n    }\n  }\n\n  assertExhausted() {\n    if (this.nextPhase < this.chunksByPhase.length) {\n      throw new InvariantError(\n        'PhasedStream expected no more phases to release but some were found.'\n      )\n    }\n  }\n}\n\nexport function prerenderClientWithPhases<T>(\n  render: () => Promise<T>,\n  finalPhase: () => void\n): Promise<T>\nexport function prerenderClientWithPhases<T>(\n  render: () => Promise<T>,\n  secondPhase: () => void,\n  finalPhase: () => void\n): Promise<T>\nexport function prerenderClientWithPhases<T>(\n  render: () => Promise<T>,\n  secondPhase: () => void,\n  thirdPhase: () => void,\n  ...remainingPhases: Array<() => void>\n): Promise<T>\nexport function prerenderClientWithPhases<T>(\n  render: () => Promise<T>,\n  ...remainingPhases: Array<() => void>\n): Promise<T> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`prerenderAndAbortInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let pendingResult: Promise<T>\n      setImmediate(() => {\n        try {\n          pendingResult = render()\n          pendingResult.catch((err) => reject(err))\n        } catch (err) {\n          reject(err)\n        }\n      })\n\n      function runFinalTask(this: () => void) {\n        try {\n          this()\n          resolve(pendingResult)\n        } catch (err) {\n          reject(err)\n        }\n      }\n\n      function runNextTask(this: () => void) {\n        try {\n          this()\n        } catch (err) {\n          reject(err)\n        }\n      }\n\n      let i = 0\n      for (; i < remainingPhases.length - 1; i++) {\n        const phase = remainingPhases[i]\n        setImmediate(runNextTask.bind(phase))\n      }\n      if (remainingPhases[i]) {\n        const finalPhase = remainingPhases[i]\n        setImmediate(runFinalTask.bind(finalPhase))\n      }\n    })\n  }\n}\n\n// React's RSC prerender function will emit an incomplete flight stream when using `prerender`. If the connection\n// closes then whatever hanging chunks exist will be errored. This is because prerender (an experimental feature)\n// has not yet implemented a concept of resume. For now we will simulate a paused connection by wrapping the stream\n// in one that doesn't close even when the underlying is complete.\nexport class ReactServerResult {\n  private _stream: null | ReadableStream<Uint8Array>\n\n  constructor(stream: ReadableStream<Uint8Array>) {\n    this._stream = stream\n  }\n\n  tee() {\n    if (this._stream === null) {\n      throw new Error(\n        'Cannot tee a ReactServerResult that has already been consumed'\n      )\n    }\n    const tee = this._stream.tee()\n    this._stream = tee[0]\n    return tee[1]\n  }\n\n  consume() {\n    if (this._stream === null) {\n      throw new Error(\n        'Cannot consume a ReactServerResult that has already been consumed'\n      )\n    }\n    const stream = this._stream\n    this._stream = null\n    return stream\n  }\n}\n\nexport type ReactServerPrerenderResolveToType = {\n  prelude: ReadableStream<Uint8Array>\n}\n\nexport async function createReactServerPrerenderResult(\n  underlying: Promise<ReactServerPrerenderResolveToType>\n): Promise<ReactServerPrerenderResult> {\n  const chunks: Array<Uint8Array> = []\n  const { prelude } = await underlying\n  const reader = prelude.getReader()\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      return new ReactServerPrerenderResult(chunks)\n    } else {\n      chunks.push(value)\n    }\n  }\n}\n\nexport async function createReactServerPrerenderResultFromRender(\n  underlying: ReadableStream<Uint8Array>\n): Promise<ReactServerPrerenderResult> {\n  const chunks: Array<Uint8Array> = []\n  const reader = underlying.getReader()\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    } else {\n      chunks.push(value)\n    }\n  }\n  return new ReactServerPrerenderResult(chunks)\n}\nexport class ReactServerPrerenderResult {\n  private _chunks: null | Array<Uint8Array>\n\n  private assertChunks(expression: string): Array<Uint8Array> {\n    if (this._chunks === null) {\n      throw new InvariantError(\n        `Cannot \\`${expression}\\` on a ReactServerPrerenderResult that has already been consumed.`\n      )\n    }\n    return this._chunks\n  }\n\n  private consumeChunks(expression: string): Array<Uint8Array> {\n    const chunks = this.assertChunks(expression)\n    this.consume()\n    return chunks\n  }\n\n  consume(): void {\n    this._chunks = null\n  }\n\n  constructor(chunks: Array<Uint8Array>) {\n    this._chunks = chunks\n  }\n\n  asUnclosingStream(): ReadableStream<Uint8Array> {\n    const chunks = this.assertChunks('asUnclosingStream()')\n    return createUnclosingStream(chunks)\n  }\n\n  consumeAsUnclosingStream(): ReadableStream<Uint8Array> {\n    const chunks = this.consumeChunks('consumeAsUnclosingStream()')\n    return createUnclosingStream(chunks)\n  }\n\n  asStream(): ReadableStream<Uint8Array> {\n    const chunks = this.assertChunks('asStream()')\n    return createClosingStream(chunks)\n  }\n\n  consumeAsStream(): ReadableStream<Uint8Array> {\n    const chunks = this.consumeChunks('consumeAsStream()')\n    return createClosingStream(chunks)\n  }\n}\n\nfunction createUnclosingStream(\n  chunks: Array<Uint8Array>\n): ReadableStream<Uint8Array> {\n  let i = 0\n  return new ReadableStream({\n    async pull(controller) {\n      if (i < chunks.length) {\n        controller.enqueue(chunks[i++])\n      }\n      // we intentionally keep the stream open. The consumer will clear\n      // out chunks once finished and the remaining memory will be GC'd\n      // when this object goes out of scope\n    },\n  })\n}\n\nfunction createClosingStream(\n  chunks: Array<Uint8Array>\n): ReadableStream<Uint8Array> {\n  let i = 0\n  return new ReadableStream({\n    async pull(controller) {\n      if (i < chunks.length) {\n        controller.enqueue(chunks[i++])\n      } else {\n        controller.close()\n      }\n    },\n  })\n}\n"], "names": ["InvariantError", "isPrerenderInterruptedError", "prerenderAndAbortInSequentialTasks", "prerender", "abort", "process", "env", "NEXT_RUNTIME", "Promise", "resolve", "reject", "pendingResult", "setImmediate", "catch", "err", "prerenderServerWithPhases", "signal", "render", "remainingPhases", "result", "addEventListener", "reason", "markInterrupted", "markComplete", "once", "ServerPrerenderStreamResult", "runFinalTask", "runNextTask", "<PERSON><PERSON><PERSON>e", "i", "length", "phase", "bind", "finalPhase", "PENDING", "COMPLETE", "INTERRUPTED", "ERRORED", "constructor", "stream", "status", "trailingChunks", "currentChunks", "chunksByPhase", "reader", "<PERSON><PERSON><PERSON><PERSON>", "progress", "done", "value", "push", "read", "then", "error", "asPhasedStream", "PhasedStream", "asStream", "ReadableStream", "start", "controller", "chunks", "j", "enqueue", "close", "destination", "nextPhase", "releasePhase", "assertExhausted", "prerenderClientWithPhases", "ReactServerResult", "_stream", "tee", "Error", "consume", "createReactServerPrerenderResult", "underlying", "prelude", "ReactServerPrerenderResult", "createReactServerPrerenderResultFromRender", "assertChunks", "expression", "_chunks", "consumeChunks", "asUnclosingStream", "createUnclosingStream", "consumeAsUnclosingStream", "createClosingStream", "consumeAsStream", "pull"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mCAAkC;AACjE,SAASC,2BAA2B,QAAQ,sBAAqB;AAEjE;;;CAGC,GACD,OAAO,SAASC,mCACdC,SAA2B,EAC3BC,KAAiB;IAEjB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIP,eACR,+EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIQ,QAAQ,CAACC,SAASC;YAC3B,IAAIC;YACJC,aAAa;gBACX,IAAI;oBACFD,gBAAgBR;oBAChBQ,cAAcE,KAAK,CAAC,KAAO;gBAC7B,EAAE,OAAOC,KAAK;oBACZJ,OAAOI;gBACT;YACF;YACAF,aAAa;gBACXR;gBACAK,QAAQE;YACV;QACF;IACF;AACF;AAoBA,OAAO,SAASI,0BACdC,MAAmB,EACnBC,MAAwC,EACxC,GAAGC,eAAkC;IAErC,IAAIb,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIP,eACR,+EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIQ,QAAQ,CAACC,SAASC;YAC3B,IAAIS;YAEJH,OAAOI,gBAAgB,CACrB,SACA;gBACE,IAAInB,4BAA4Be,OAAOK,MAAM,GAAG;oBAC9CF,OAAOG,eAAe;gBACxB,OAAO;oBACLH,OAAOI,YAAY;gBACrB;YACF,GACA;gBACEC,MAAM;YACR;YAGFZ,aAAa;gBACX,IAAI;oBACFO,SAAS,IAAIM,4BAA4BR;gBAC3C,EAAE,OAAOH,KAAK;oBACZJ,OAAOI;gBACT;YACF;YAEA,SAASY;gBACP,IAAI;oBACF,IAAIP,QAAQ;wBACVA,OAAOI,YAAY;wBACnB,IAAI;oBACN;oBACAd,QAAQU;gBACV,EAAE,OAAOL,KAAK;oBACZJ,OAAOI;gBACT;YACF;YAEA,SAASa;gBACP,IAAI;oBACF,IAAIR,QAAQ;wBACVA,OAAOS,SAAS;wBAChB,IAAI;oBACN;gBACF,EAAE,OAAOd,KAAK;oBACZJ,OAAOI;gBACT;YACF;YAEA,IAAIe,IAAI;YACR,MAAOA,IAAIX,gBAAgBY,MAAM,GAAG,GAAGD,IAAK;gBAC1C,MAAME,QAAQb,eAAe,CAACW,EAAE;gBAChCjB,aAAae,YAAYK,IAAI,CAACD;YAChC;YACA,IAAIb,eAAe,CAACW,EAAE,EAAE;gBACtB,MAAMI,aAAaf,eAAe,CAACW,EAAE;gBACrCjB,aAAac,aAAaM,IAAI,CAACC;YACjC;QACF;IACF;AACF;AAEA,MAAMC,UAAU;AAChB,MAAMC,WAAW;AACjB,MAAMC,cAAc;AACpB,MAAMC,UAAU;AAEhB,OAAO,MAAMZ;IAOXa,YAAYC,MAAkC,CAAE;QAC9C,IAAI,CAACC,MAAM,GAAGN;QACd,IAAI,CAACb,MAAM,GAAG;QAEd,IAAI,CAACoB,cAAc,GAAG,EAAE;QACxB,IAAI,CAACC,aAAa,GAAG,EAAE;QACvB,IAAI,CAACC,aAAa,GAAG;YAAC,IAAI,CAACD,aAAa;SAAC;QAEzC,MAAME,SAASL,OAAOM,SAAS;QAE/B,MAAMC,WAAW,CAAC,EAChBC,IAAI,EACJC,KAAK,EACgC;YACrC,IAAID,MAAM;gBACR,IAAI,IAAI,CAACP,MAAM,KAAKN,SAAS;oBAC3B,IAAI,CAACM,MAAM,GAAGL;gBAChB;gBACA;YACF;YACA,IAAI,IAAI,CAACK,MAAM,KAAKN,WAAW,IAAI,CAACM,MAAM,KAAKJ,aAAa;gBAC1D,IAAI,CAACM,aAAa,CAACO,IAAI,CAACD;YAC1B,OAAO;gBACL,IAAI,CAACP,cAAc,CAACQ,IAAI,CAACD;YAC3B;YACAJ,OAAOM,IAAI,GAAGC,IAAI,CAACL,UAAUM;QAC/B;QACA,MAAMA,QAAQ,CAAC/B;YACb,IAAI,CAACmB,MAAM,GAAGH;YACd,IAAI,CAAChB,MAAM,GAAGA;QAChB;QAEAuB,OAAOM,IAAI,GAAGC,IAAI,CAACL,UAAUM;IAC/B;IAEAxB,YAAY;QACV,IAAI,CAACc,aAAa,GAAG,EAAE;QACvB,IAAI,CAACC,aAAa,CAACM,IAAI,CAAC,IAAI,CAACP,aAAa;IAC5C;IAEAnB,eAAe;QACb,IAAI,IAAI,CAACiB,MAAM,KAAKN,SAAS;YAC3B,IAAI,CAACM,MAAM,GAAGL;QAChB;IACF;IAEAb,kBAAkB;QAChB,IAAI,CAACkB,MAAM,GAAGJ;IAChB;IAEA;;;;;;GAMC,GACDiB,iBAAiB;QACf,OAAQ,IAAI,CAACb,MAAM;YACjB,KAAKL;YACL,KAAKC;gBACH,OAAO,IAAIkB,aAAa,IAAI,CAACX,aAAa;YAC5C;gBACE,MAAM,qBAEL,CAFK,IAAI3C,eACR,CAAC,mGAAmG,EAAE,IAAI,CAACwC,MAAM,EAAE,GAD/G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;QACJ;IACF;IAEA;;;GAGC,GACDe,WAAW;QACT,OAAQ,IAAI,CAACf,MAAM;YACjB,KAAKL;YACL,KAAKC;gBACH,MAAMO,gBAAgB,IAAI,CAACA,aAAa;gBACxC,MAAMF,iBAAiB,IAAI,CAACA,cAAc;gBAC1C,OAAO,IAAIe,eAAe;oBACxBC,OAAMC,UAAU;wBACd,IAAK,IAAI7B,IAAI,GAAGA,IAAIc,cAAcb,MAAM,EAAED,IAAK;4BAC7C,MAAM8B,SAAShB,aAAa,CAACd,EAAE;4BAC/B,IAAK,IAAI+B,IAAI,GAAGA,IAAID,OAAO7B,MAAM,EAAE8B,IAAK;gCACtCF,WAAWG,OAAO,CAACF,MAAM,CAACC,EAAE;4BAC9B;wBACF;wBACA,IAAK,IAAI/B,IAAI,GAAGA,IAAIY,eAAeX,MAAM,EAAED,IAAK;4BAC9C6B,WAAWG,OAAO,CAACpB,cAAc,CAACZ,EAAE;wBACtC;wBACA6B,WAAWI,KAAK;oBAClB;gBACF;YACF;gBACE,MAAM,qBAEL,CAFK,IAAI9D,eACR,CAAC,mGAAmG,EAAE,IAAI,CAACwC,MAAM,EAAE,GAD/G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;QACJ;IACF;AACF;AAEA,MAAMc,qBAAwBE;IAK5BlB,YAAYK,aAA8B,CAAE;QAC1C,IAAIA,cAAcb,MAAM,KAAK,GAAG;YAC9B,MAAM,qBAEL,CAFK,IAAI9B,eACR,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI+D;QACJ,KAAK,CAAC;YACJN,OAAMC,UAAU;gBACdK,cAAcL;YAChB;QACF;QAEA,4GAA4G;QAC5G,uGAAuG;QACvG,IAAI,CAACK,WAAW,GAAGA;QACnB,IAAI,CAACC,SAAS,GAAG;QACjB,IAAI,CAACrB,aAAa,GAAGA;QACrB,IAAI,CAACsB,YAAY;IACnB;IAEAA,eAAe;QACb,IAAI,IAAI,CAACD,SAAS,GAAG,IAAI,CAACrB,aAAa,CAACb,MAAM,EAAE;YAC9C,MAAM6B,SAAS,IAAI,CAAChB,aAAa,CAAC,IAAI,CAACqB,SAAS,GAAG;YACnD,IAAK,IAAInC,IAAI,GAAGA,IAAI8B,OAAO7B,MAAM,EAAED,IAAK;gBACtC,IAAI,CAACkC,WAAW,CAACF,OAAO,CAACF,MAAM,CAAC9B,EAAE;YACpC;QACF,OAAO;YACL,MAAM,qBAEL,CAFK,IAAI7B,eACR,sEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEAkE,kBAAkB;QAChB,IAAI,IAAI,CAACF,SAAS,GAAG,IAAI,CAACrB,aAAa,CAACb,MAAM,EAAE;YAC9C,MAAM,qBAEL,CAFK,IAAI9B,eACR,yEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF;AAiBA,OAAO,SAASmE,0BACdlD,MAAwB,EACxB,GAAGC,eAAkC;IAErC,IAAIb,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIP,eACR,+EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIQ,QAAQ,CAACC,SAASC;YAC3B,IAAIC;YACJC,aAAa;gBACX,IAAI;oBACFD,gBAAgBM;oBAChBN,cAAcE,KAAK,CAAC,CAACC,MAAQJ,OAAOI;gBACtC,EAAE,OAAOA,KAAK;oBACZJ,OAAOI;gBACT;YACF;YAEA,SAASY;gBACP,IAAI;oBACF,IAAI;oBACJjB,QAAQE;gBACV,EAAE,OAAOG,KAAK;oBACZJ,OAAOI;gBACT;YACF;YAEA,SAASa;gBACP,IAAI;oBACF,IAAI;gBACN,EAAE,OAAOb,KAAK;oBACZJ,OAAOI;gBACT;YACF;YAEA,IAAIe,IAAI;YACR,MAAOA,IAAIX,gBAAgBY,MAAM,GAAG,GAAGD,IAAK;gBAC1C,MAAME,QAAQb,eAAe,CAACW,EAAE;gBAChCjB,aAAae,YAAYK,IAAI,CAACD;YAChC;YACA,IAAIb,eAAe,CAACW,EAAE,EAAE;gBACtB,MAAMI,aAAaf,eAAe,CAACW,EAAE;gBACrCjB,aAAac,aAAaM,IAAI,CAACC;YACjC;QACF;IACF;AACF;AAEA,iHAAiH;AACjH,iHAAiH;AACjH,mHAAmH;AACnH,kEAAkE;AAClE,OAAO,MAAMmC;IAGX9B,YAAYC,MAAkC,CAAE;QAC9C,IAAI,CAAC8B,OAAO,GAAG9B;IACjB;IAEA+B,MAAM;QACJ,IAAI,IAAI,CAACD,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAIE,MACR,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMD,MAAM,IAAI,CAACD,OAAO,CAACC,GAAG;QAC5B,IAAI,CAACD,OAAO,GAAGC,GAAG,CAAC,EAAE;QACrB,OAAOA,GAAG,CAAC,EAAE;IACf;IAEAE,UAAU;QACR,IAAI,IAAI,CAACH,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAIE,MACR,sEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMhC,SAAS,IAAI,CAAC8B,OAAO;QAC3B,IAAI,CAACA,OAAO,GAAG;QACf,OAAO9B;IACT;AACF;AAMA,OAAO,eAAekC,iCACpBC,UAAsD;IAEtD,MAAMf,SAA4B,EAAE;IACpC,MAAM,EAAEgB,OAAO,EAAE,GAAG,MAAMD;IAC1B,MAAM9B,SAAS+B,QAAQ9B,SAAS;IAChC,MAAO,KAAM;QACX,MAAM,EAAEE,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOM,IAAI;QACzC,IAAIH,MAAM;YACR,OAAO,IAAI6B,2BAA2BjB;QACxC,OAAO;YACLA,OAAOV,IAAI,CAACD;QACd;IACF;AACF;AAEA,OAAO,eAAe6B,2CACpBH,UAAsC;IAEtC,MAAMf,SAA4B,EAAE;IACpC,MAAMf,SAAS8B,WAAW7B,SAAS;IACnC,MAAO,KAAM;QACX,MAAM,EAAEE,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOM,IAAI;QACzC,IAAIH,MAAM;YACR;QACF,OAAO;YACLY,OAAOV,IAAI,CAACD;QACd;IACF;IACA,OAAO,IAAI4B,2BAA2BjB;AACxC;AACA,OAAO,MAAMiB;IAGHE,aAAaC,UAAkB,EAAqB;QAC1D,IAAI,IAAI,CAACC,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAIhF,eACR,CAAC,SAAS,EAAE+E,WAAW,kEAAkE,CAAC,GADtF,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAO,IAAI,CAACC,OAAO;IACrB;IAEQC,cAAcF,UAAkB,EAAqB;QAC3D,MAAMpB,SAAS,IAAI,CAACmB,YAAY,CAACC;QACjC,IAAI,CAACP,OAAO;QACZ,OAAOb;IACT;IAEAa,UAAgB;QACd,IAAI,CAACQ,OAAO,GAAG;IACjB;IAEA1C,YAAYqB,MAAyB,CAAE;QACrC,IAAI,CAACqB,OAAO,GAAGrB;IACjB;IAEAuB,oBAAgD;QAC9C,MAAMvB,SAAS,IAAI,CAACmB,YAAY,CAAC;QACjC,OAAOK,sBAAsBxB;IAC/B;IAEAyB,2BAAuD;QACrD,MAAMzB,SAAS,IAAI,CAACsB,aAAa,CAAC;QAClC,OAAOE,sBAAsBxB;IAC/B;IAEAJ,WAAuC;QACrC,MAAMI,SAAS,IAAI,CAACmB,YAAY,CAAC;QACjC,OAAOO,oBAAoB1B;IAC7B;IAEA2B,kBAA8C;QAC5C,MAAM3B,SAAS,IAAI,CAACsB,aAAa,CAAC;QAClC,OAAOI,oBAAoB1B;IAC7B;AACF;AAEA,SAASwB,sBACPxB,MAAyB;IAEzB,IAAI9B,IAAI;IACR,OAAO,IAAI2B,eAAe;QACxB,MAAM+B,MAAK7B,UAAU;YACnB,IAAI7B,IAAI8B,OAAO7B,MAAM,EAAE;gBACrB4B,WAAWG,OAAO,CAACF,MAAM,CAAC9B,IAAI;YAChC;QACA,iEAAiE;QACjE,iEAAiE;QACjE,qCAAqC;QACvC;IACF;AACF;AAEA,SAASwD,oBACP1B,MAAyB;IAEzB,IAAI9B,IAAI;IACR,OAAO,IAAI2B,eAAe;QACxB,MAAM+B,MAAK7B,UAAU;YACnB,IAAI7B,IAAI8B,OAAO7B,MAAM,EAAE;gBACrB4B,WAAWG,OAAO,CAACF,MAAM,CAAC9B,IAAI;YAChC,OAAO;gBACL6B,WAAWI,KAAK;YAClB;QACF;IACF;AACF"}