{"version": 3, "sources": ["../../../../src/server/lib/router-utils/decode-path-params.ts"], "sourcesContent": ["import escapePathDelimiters from '../../../shared/lib/router/utils/escape-path-delimiters'\nimport { DecodeError } from '../../../shared/lib/utils'\n\n/**\n * We only encode path delimiters for path segments from\n * getStaticPaths so we need to attempt decoding the URL\n * to match against and only escape the path delimiters\n * this allows non-ascii values to be handled e.g.\n * Japanese characters.\n * */\nfunction decodePathParams(pathname: string): string {\n  // TODO: investigate adding this handling for non-SSG\n  // pages so non-ascii names also work there.\n  return pathname\n    .split('/')\n    .map((seg) => {\n      try {\n        seg = escapePathDelimiters(decodeURIComponent(seg), true)\n      } catch (_) {\n        // An improperly encoded URL was provided\n        throw new DecodeError('Failed to decode path param(s).')\n      }\n      return seg\n    })\n    .join('/')\n}\n\nexport { decodePathParams }\n"], "names": ["escapePathDelimiters", "DecodeError", "decodePathParams", "pathname", "split", "map", "seg", "decodeURIComponent", "_", "join"], "mappings": "AAAA,OAAOA,0BAA0B,0DAAyD;AAC1F,SAASC,WAAW,QAAQ,4BAA2B;AAEvD;;;;;;GAMG,GACH,SAASC,iBAAiBC,QAAgB;IACxC,qDAAqD;IACrD,4CAA4C;IAC5C,OAAOA,SACJC,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC;QACJ,IAAI;YACFA,MAAMN,qBAAqBO,mBAAmBD,MAAM;QACtD,EAAE,OAAOE,GAAG;YACV,yCAAyC;YACzC,MAAM,qBAAkD,CAAlD,IAAIP,YAAY,oCAAhB,qBAAA;uBAAA;4BAAA;8BAAA;YAAiD;QACzD;QACA,OAAOK;IACT,GACCG,IAAI,CAAC;AACV;AAEA,SAASP,gBAAgB,GAAE"}