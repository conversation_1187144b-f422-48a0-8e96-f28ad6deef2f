{"version": 3, "sources": ["../../src/server/crypto-utils.ts"], "sourcesContent": ["import crypto from 'crypto'\n\n// Background:\n// https://security.stackexchange.com/questions/184305/why-would-i-ever-use-aes-256-cbc-if-aes-256-gcm-is-more-secure\n\nconst CIPHER_ALGORITHM = `aes-256-gcm`,\n  CIPHER_KEY_LENGTH = 32, // https://stackoverflow.com/a/28307668/4397028\n  CIPHER_IV_LENGTH = 16, // https://stackoverflow.com/a/28307668/4397028\n  CIPHER_TAG_LENGTH = 16,\n  CIPHER_SALT_LENGTH = 64\n\nconst PBKDF2_ITERATIONS = 100_000 // https://support.1password.com/pbkdf2/\n\nexport function encryptWithSecret(secret: Buffer, data: string): string {\n  const iv = crypto.randomBytes(CIPHER_IV_LENGTH)\n  const salt = crypto.randomBytes(CIPHER_SALT_LENGTH)\n\n  // https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n  const key = crypto.pbkdf2Sync(\n    secret,\n    salt,\n    PBKDF2_ITERATIONS,\n    CIPHER_KEY_LENGTH,\n    `sha512`\n  )\n\n  const cipher = crypto.createCipheriv(CIPHER_ALGORITHM, key, iv)\n  const encrypted = Buffer.concat([cipher.update(data, `utf8`), cipher.final()])\n\n  // https://nodejs.org/api/crypto.html#crypto_cipher_getauthtag\n  const tag = cipher.getAuthTag()\n\n  return Buffer.concat([\n    // Data as required by:\n    // Salt for Key: https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n    // IV: https://nodejs.org/api/crypto.html#crypto_class_decipher\n    // Tag: https://nodejs.org/api/crypto.html#crypto_decipher_setauthtag_buffer\n    salt,\n    iv,\n    tag,\n    encrypted,\n  ]).toString(`hex`)\n}\n\nexport function decryptWithSecret(\n  secret: Buffer,\n  encryptedData: string\n): string {\n  const buffer = Buffer.from(encryptedData, `hex`)\n\n  const salt = buffer.slice(0, CIPHER_SALT_LENGTH)\n  const iv = buffer.slice(\n    CIPHER_SALT_LENGTH,\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH\n  )\n  const tag = buffer.slice(\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH,\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH + CIPHER_TAG_LENGTH\n  )\n  const encrypted = buffer.slice(\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH + CIPHER_TAG_LENGTH\n  )\n\n  // https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n  const key = crypto.pbkdf2Sync(\n    secret,\n    salt,\n    PBKDF2_ITERATIONS,\n    CIPHER_KEY_LENGTH,\n    `sha512`\n  )\n\n  const decipher = crypto.createDecipheriv(CIPHER_ALGORITHM, key, iv)\n  decipher.setAuthTag(tag)\n\n  return decipher.update(encrypted) + decipher.final(`utf8`)\n}\n"], "names": ["crypto", "CIPHER_ALGORITHM", "CIPHER_KEY_LENGTH", "CIPHER_IV_LENGTH", "CIPHER_TAG_LENGTH", "CIPHER_SALT_LENGTH", "PBKDF2_ITERATIONS", "encryptWithSecret", "secret", "data", "iv", "randomBytes", "salt", "key", "pbkdf2Sync", "cipher", "createCipheriv", "encrypted", "<PERSON><PERSON><PERSON>", "concat", "update", "final", "tag", "getAuthTag", "toString", "decryptWithSecret", "encryptedData", "buffer", "from", "slice", "decipher", "createDecipheriv", "setAuthTag"], "mappings": "AAAA,OAAOA,YAAY,SAAQ;AAE3B,cAAc;AACd,qHAAqH;AAErH,MAAMC,mBAAmB,CAAC,WAAW,CAAC,EACpCC,oBAAoB,IACpBC,mBAAmB,IACnBC,oBAAoB,IACpBC,qBAAqB;AAEvB,MAAMC,oBAAoB,OAAQ,wCAAwC;;AAE1E,OAAO,SAASC,kBAAkBC,MAAc,EAAEC,IAAY;IAC5D,MAAMC,KAAKV,OAAOW,WAAW,CAACR;IAC9B,MAAMS,OAAOZ,OAAOW,WAAW,CAACN;IAEhC,qGAAqG;IACrG,MAAMQ,MAAMb,OAAOc,UAAU,CAC3BN,QACAI,MACAN,mBACAJ,mBACA,CAAC,MAAM,CAAC;IAGV,MAAMa,SAASf,OAAOgB,cAAc,CAACf,kBAAkBY,KAAKH;IAC5D,MAAMO,YAAYC,OAAOC,MAAM,CAAC;QAACJ,OAAOK,MAAM,CAACX,MAAM,CAAC,IAAI,CAAC;QAAGM,OAAOM,KAAK;KAAG;IAE7E,8DAA8D;IAC9D,MAAMC,MAAMP,OAAOQ,UAAU;IAE7B,OAAOL,OAAOC,MAAM,CAAC;QACnB,uBAAuB;QACvB,mHAAmH;QACnH,+DAA+D;QAC/D,4EAA4E;QAC5EP;QACAF;QACAY;QACAL;KACD,EAAEO,QAAQ,CAAC,CAAC,GAAG,CAAC;AACnB;AAEA,OAAO,SAASC,kBACdjB,MAAc,EACdkB,aAAqB;IAErB,MAAMC,SAAST,OAAOU,IAAI,CAACF,eAAe,CAAC,GAAG,CAAC;IAE/C,MAAMd,OAAOe,OAAOE,KAAK,CAAC,GAAGxB;IAC7B,MAAMK,KAAKiB,OAAOE,KAAK,CACrBxB,oBACAA,qBAAqBF;IAEvB,MAAMmB,MAAMK,OAAOE,KAAK,CACtBxB,qBAAqBF,kBACrBE,qBAAqBF,mBAAmBC;IAE1C,MAAMa,YAAYU,OAAOE,KAAK,CAC5BxB,qBAAqBF,mBAAmBC;IAG1C,qGAAqG;IACrG,MAAMS,MAAMb,OAAOc,UAAU,CAC3BN,QACAI,MACAN,mBACAJ,mBACA,CAAC,MAAM,CAAC;IAGV,MAAM4B,WAAW9B,OAAO+B,gBAAgB,CAAC9B,kBAAkBY,KAAKH;IAChEoB,SAASE,UAAU,CAACV;IAEpB,OAAOQ,SAASV,MAAM,CAACH,aAAaa,SAAST,KAAK,CAAC,CAAC,IAAI,CAAC;AAC3D"}