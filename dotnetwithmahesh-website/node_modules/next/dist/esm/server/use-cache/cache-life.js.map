{"version": 3, "sources": ["../../../src/server/use-cache/cache-life.ts"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\n\nexport type CacheLife = {\n  // How long the client can cache a value without checking with the server.\n  stale?: number\n  // How frequently you want the cache to refresh on the server.\n  // Stale values may be served while revalidating.\n  revalidate?: number\n  // In the worst case scenario, where you haven't had traffic in a while,\n  // how stale can a value be until you prefer deopting to dynamic.\n  // Must be longer than revalidate.\n  expire?: number\n}\n// The equivalent header is kind of like:\n// Cache-Control: max-age=[stale],s-max-age=[revalidate],stale-while-revalidate=[expire-revalidate],stale-if-error=[expire-revalidate]\n// Except that stale-while-revalidate/stale-if-error only applies to shared caches - not private caches.\n\n// The default revalidates relatively frequently but doesn't expire to ensure it's always\n// able to serve fast results but by default doesn't hang.\n\n// This gets overridden by the next-types-plugin\ntype CacheLifeProfiles =\n  | 'default'\n  | 'seconds'\n  | 'minutes'\n  | 'hours'\n  | 'days'\n  | 'weeks'\n  | 'max'\n  | (string & {})\n\nfunction validateCacheLife(profile: CacheLife) {\n  if (profile.stale !== undefined) {\n    if ((profile.stale as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you want to cache on the client forever ' +\n          'without checking with the server.'\n      )\n    } else if (typeof profile.stale !== 'number') {\n      throw new Error('The stale option must be a number of seconds.')\n    }\n  }\n  if (profile.revalidate !== undefined) {\n    if ((profile.revalidate as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you do not want to revalidate by time.'\n      )\n    } else if (typeof profile.revalidate !== 'number') {\n      throw new Error('The revalidate option must be a number of seconds.')\n    }\n  }\n  if (profile.expire !== undefined) {\n    if ((profile.expire as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you want to cache on the server forever ' +\n          'without checking with the origin.'\n      )\n    } else if (typeof profile.expire !== 'number') {\n      throw new Error('The expire option must be a number of seconds.')\n    }\n  }\n\n  if (profile.revalidate !== undefined && profile.expire !== undefined) {\n    if (profile.revalidate > profile.expire) {\n      throw new Error(\n        'If providing both the revalidate and expire options, ' +\n          'the expire option must be greater than the revalidate option. ' +\n          'The expire option indicates how many seconds from the start ' +\n          'until it can no longer be used.'\n      )\n    }\n  }\n\n  if (profile.stale !== undefined && profile.expire !== undefined) {\n    if (profile.stale > profile.expire) {\n      throw new Error(\n        'If providing both the stale and expire options, ' +\n          'the expire option must be greater than the stale option. ' +\n          'The expire option indicates how many seconds from the start ' +\n          'until it can no longer be used.'\n      )\n    }\n  }\n}\n\nexport function cacheLife(profile: CacheLifeProfiles | CacheLife): void {\n  if (!process.env.__NEXT_USE_CACHE) {\n    throw new Error(\n      'cacheLife() is only available with the experimental.useCache config.'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore || workUnitStore.type !== 'cache') {\n    throw new Error(\n      'cacheLife() can only be called inside a \"use cache\" function.'\n    )\n  }\n\n  if (typeof profile === 'string') {\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) {\n      throw new Error(\n        'cacheLife() can only be called during App Router rendering at the moment.'\n      )\n    }\n    if (!workStore.cacheLifeProfiles) {\n      throw new Error(\n        'cacheLifeProfiles should always be provided. This is a bug in Next.js.'\n      )\n    }\n\n    // TODO: This should be globally available and not require an AsyncLocalStorage.\n    const configuredProfile = workStore.cacheLifeProfiles[profile]\n    if (configuredProfile === undefined) {\n      if (workStore.cacheLifeProfiles[profile.trim()]) {\n        throw new Error(\n          `Unknown cacheLife profile \"${profile}\" is not configured in next.config.js\\n` +\n            `Did you mean \"${profile.trim()}\" without the spaces?`\n        )\n      }\n      throw new Error(\n        `Unknown cacheLife profile \"${profile}\" is not configured in next.config.js\\n` +\n          'module.exports = {\\n' +\n          '  experimental: {\\n' +\n          '    cacheLife: {\\n' +\n          `      \"${profile}\": ...\\n` +\n          '    }\\n' +\n          '  }\\n' +\n          '}'\n      )\n    }\n    profile = configuredProfile\n  } else if (\n    typeof profile !== 'object' ||\n    profile === null ||\n    Array.isArray(profile)\n  ) {\n    throw new Error(\n      'Invalid cacheLife() option. Either pass a profile name or object.'\n    )\n  } else {\n    validateCacheLife(profile)\n  }\n\n  if (profile.revalidate !== undefined) {\n    // Track the explicit revalidate time.\n    if (\n      workUnitStore.explicitRevalidate === undefined ||\n      workUnitStore.explicitRevalidate > profile.revalidate\n    ) {\n      workUnitStore.explicitRevalidate = profile.revalidate\n    }\n  }\n  if (profile.expire !== undefined) {\n    // Track the explicit expire time.\n    if (\n      workUnitStore.explicitExpire === undefined ||\n      workUnitStore.explicitExpire > profile.expire\n    ) {\n      workUnitStore.explicitExpire = profile.expire\n    }\n  }\n  if (profile.stale !== undefined) {\n    // Track the explicit stale time.\n    if (\n      workUnitStore.explicitStale === undefined ||\n      workUnitStore.explicitStale > profile.stale\n    ) {\n      workUnitStore.explicitStale = profile.stale\n    }\n  }\n}\n"], "names": ["workAsyncStorage", "workUnitAsyncStorage", "validateCacheLife", "profile", "stale", "undefined", "Error", "revalidate", "expire", "cacheLife", "process", "env", "__NEXT_USE_CACHE", "workUnitStore", "getStore", "type", "workStore", "cacheLifeProfiles", "configuredProfile", "trim", "Array", "isArray", "explicitRevalidate", "explicitExpire", "explicitStale"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,oBAAoB,QAAQ,iDAAgD;AA+BrF,SAASC,kBAAkBC,OAAkB;IAC3C,IAAIA,QAAQC,KAAK,KAAKC,WAAW;QAC/B,IAAI,AAACF,QAAQC,KAAK,KAAa,OAAO;YACpC,MAAM,qBAGL,CAHK,IAAIE,MACR,mFACE,sCAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF,OAAO,IAAI,OAAOH,QAAQC,KAAK,KAAK,UAAU;YAC5C,MAAM,qBAA0D,CAA1D,IAAIE,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;IACF;IACA,IAAIH,QAAQI,UAAU,KAAKF,WAAW;QACpC,IAAI,AAACF,QAAQI,UAAU,KAAa,OAAO;YACzC,MAAM,qBAEL,CAFK,IAAID,MACR,iFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAI,OAAOH,QAAQI,UAAU,KAAK,UAAU;YACjD,MAAM,qBAA+D,CAA/D,IAAID,MAAM,uDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA8D;QACtE;IACF;IACA,IAAIH,QAAQK,MAAM,KAAKH,WAAW;QAChC,IAAI,AAACF,QAAQK,MAAM,KAAa,OAAO;YACrC,MAAM,qBAGL,CAHK,IAAIF,MACR,mFACE,sCAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF,OAAO,IAAI,OAAOH,QAAQK,MAAM,KAAK,UAAU;YAC7C,MAAM,qBAA2D,CAA3D,IAAIF,MAAM,mDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;IACF;IAEA,IAAIH,QAAQI,UAAU,KAAKF,aAAaF,QAAQK,MAAM,KAAKH,WAAW;QACpE,IAAIF,QAAQI,UAAU,GAAGJ,QAAQK,MAAM,EAAE;YACvC,MAAM,qBAKL,CALK,IAAIF,MACR,0DACE,mEACA,iEACA,oCAJE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;IACF;IAEA,IAAIH,QAAQC,KAAK,KAAKC,aAAaF,QAAQK,MAAM,KAAKH,WAAW;QAC/D,IAAIF,QAAQC,KAAK,GAAGD,QAAQK,MAAM,EAAE;YAClC,MAAM,qBAKL,CALK,IAAIF,MACR,qDACE,8DACA,iEACA,oCAJE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;IACF;AACF;AAEA,OAAO,SAASG,UAAUN,OAAsC;IAC9D,IAAI,CAACO,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QACjC,MAAM,qBAEL,CAFK,IAAIN,MACR,yEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMO,gBAAgBZ,qBAAqBa,QAAQ;IACnD,IAAI,CAACD,iBAAiBA,cAAcE,IAAI,KAAK,SAAS;QACpD,MAAM,qBAEL,CAFK,IAAIT,MACR,kEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,MAAMa,YAAYhB,iBAAiBc,QAAQ;QAC3C,IAAI,CAACE,WAAW;YACd,MAAM,qBAEL,CAFK,IAAIV,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAI,CAACU,UAAUC,iBAAiB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIX,MACR,2EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,gFAAgF;QAChF,MAAMY,oBAAoBF,UAAUC,iBAAiB,CAACd,QAAQ;QAC9D,IAAIe,sBAAsBb,WAAW;YACnC,IAAIW,UAAUC,iBAAiB,CAACd,QAAQgB,IAAI,GAAG,EAAE;gBAC/C,MAAM,qBAGL,CAHK,IAAIb,MACR,CAAC,2BAA2B,EAAEH,QAAQ,uCAAuC,CAAC,GAC5E,CAAC,cAAc,EAAEA,QAAQgB,IAAI,GAAG,qBAAqB,CAAC,GAFpD,qBAAA;2BAAA;gCAAA;kCAAA;gBAGN;YACF;YACA,MAAM,qBASL,CATK,IAAIb,MACR,CAAC,2BAA2B,EAAEH,QAAQ,uCAAuC,CAAC,GAC5E,yBACA,wBACA,uBACA,CAAC,OAAO,EAAEA,QAAQ,QAAQ,CAAC,GAC3B,YACA,UACA,MARE,qBAAA;uBAAA;4BAAA;8BAAA;YASN;QACF;QACAA,UAAUe;IACZ,OAAO,IACL,OAAOf,YAAY,YACnBA,YAAY,QACZiB,MAAMC,OAAO,CAAClB,UACd;QACA,MAAM,qBAEL,CAFK,IAAIG,MACR,sEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACLJ,kBAAkBC;IACpB;IAEA,IAAIA,QAAQI,UAAU,KAAKF,WAAW;QACpC,sCAAsC;QACtC,IACEQ,cAAcS,kBAAkB,KAAKjB,aACrCQ,cAAcS,kBAAkB,GAAGnB,QAAQI,UAAU,EACrD;YACAM,cAAcS,kBAAkB,GAAGnB,QAAQI,UAAU;QACvD;IACF;IACA,IAAIJ,QAAQK,MAAM,KAAKH,WAAW;QAChC,kCAAkC;QAClC,IACEQ,cAAcU,cAAc,KAAKlB,aACjCQ,cAAcU,cAAc,GAAGpB,QAAQK,MAAM,EAC7C;YACAK,cAAcU,cAAc,GAAGpB,QAAQK,MAAM;QAC/C;IACF;IACA,IAAIL,QAAQC,KAAK,KAAKC,WAAW;QAC/B,iCAAiC;QACjC,IACEQ,cAAcW,aAAa,KAAKnB,aAChCQ,cAAcW,aAAa,GAAGrB,QAAQC,KAAK,EAC3C;YACAS,cAAcW,aAAa,GAAGrB,QAAQC,KAAK;QAC7C;IACF;AACF"}