{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/dev/dev-app-route-route-matcher-provider.ts"], "sourcesContent": ["import type { <PERSON><PERSON>eader } from './helpers/file-reader/file-reader'\nimport type { Normalizer } from '../../normalizers/normalizer'\nimport { AppRouteRouteMatcher } from '../../route-matchers/app-route-route-matcher'\nimport { RouteKind } from '../../route-kind'\nimport { FileCacheRouteMatcherProvider } from './file-cache-route-matcher-provider'\nimport { isAppRouteRoute } from '../../../lib/is-app-route-route'\nimport { DevAppNormalizers } from '../../normalizers/built/app'\nimport {\n  isMetadataRouteFile,\n  isStaticMetadataRoute,\n} from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport path from '../../../shared/lib/isomorphic/path'\n\nexport class DevAppRouteRouteMatcherProvider extends FileCacheRouteMatcherProvider<AppRouteRouteMatcher> {\n  private readonly normalizers: {\n    page: Normalizer\n    pathname: Normalizer\n    bundlePath: Normalizer\n  }\n  private readonly appDir: string\n\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    reader: FileReader\n  ) {\n    super(appDir, reader)\n\n    this.appDir = appDir\n    this.normalizers = new DevAppNormalizers(appDir, extensions)\n  }\n\n  protected async transform(\n    files: ReadonlyArray<string>\n  ): Promise<ReadonlyArray<AppRouteRouteMatcher>> {\n    const matchers: Array<AppRouteRouteMatcher> = []\n    for (const filename of files) {\n      const page = this.normalizers.page.normalize(filename)\n\n      // If the file isn't a match for this matcher, then skip it.\n      if (!isAppRouteRoute(page)) continue\n\n      // Validate that this is not an ignored page.\n      if (page.includes('/_')) continue\n\n      const pathname = this.normalizers.pathname.normalize(filename)\n      const bundlePath = this.normalizers.bundlePath.normalize(filename)\n      const ext = path.extname(filename).slice(1)\n      const isEntryMetadataRouteFile = isMetadataRouteFile(\n        filename.replace(this.appDir, ''),\n        [ext],\n        true\n      )\n\n      if (isEntryMetadataRouteFile && !isStaticMetadataRoute(page)) {\n        // Matching dynamic metadata routes.\n        // Add 2 possibilities for both single and multiple routes:\n        {\n          // single:\n          // /sitemap.ts -> /sitemap.xml/route\n          // /icon.ts -> /icon/route\n          // We'll map the filename before normalization:\n          // sitemap.ts -> sitemap.xml/route.ts\n          // icon.ts -> icon/route.ts\n          const metadataPage = normalizeMetadataPageToRoute(page, false)\n          const metadataPathname = normalizeMetadataPageToRoute(pathname, false)\n          const metadataBundlePath = normalizeMetadataPageToRoute(\n            bundlePath,\n            false\n          )\n\n          const matcher = new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page: metadataPage,\n            pathname: metadataPathname,\n            bundlePath: metadataBundlePath,\n            filename,\n          })\n          matchers.push(matcher)\n        }\n        {\n          // multiple:\n          // /sitemap.ts -> /sitemap/[__metadata_id__]/route\n          // /icon.ts -> /icon/[__metadata_id__]/route\n          // We'll map the filename before normalization:\n          // sitemap.ts -> sitemap.xml/[__metadata_id__].ts\n          // icon.ts -> icon/[__metadata_id__].ts\n          const metadataPage = normalizeMetadataPageToRoute(page, true)\n          const metadataPathname = normalizeMetadataPageToRoute(pathname, true)\n          const metadataBundlePath = normalizeMetadataPageToRoute(\n            bundlePath,\n            true\n          )\n\n          const matcher = new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page: metadataPage,\n            pathname: metadataPathname,\n            bundlePath: metadataBundlePath,\n            filename,\n          })\n          matchers.push(matcher)\n        }\n      } else {\n        // Normal app routes and static metadata routes.\n        matchers.push(\n          new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page,\n            pathname,\n            bundlePath,\n            filename,\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["AppRouteRouteMatcher", "RouteKind", "FileCacheRouteMatcherProvider", "isAppRouteRoute", "DevAppNormalizers", "isMetadataRouteFile", "isStaticMetadataRoute", "normalizeMetadataPageToRoute", "path", "DevAppRouteRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "normalizers", "transform", "files", "matchers", "filename", "page", "normalize", "includes", "pathname", "bundlePath", "ext", "extname", "slice", "isEntryMetadataRouteFile", "replace", "metadataPage", "metadataPathname", "metadataBundlePath", "matcher", "kind", "APP_ROUTE", "push"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,+CAA8C;AACnF,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SAASC,6BAA6B,QAAQ,sCAAqC;AACnF,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SAASC,iBAAiB,QAAQ,8BAA6B;AAC/D,SACEC,mBAAmB,EACnBC,qBAAqB,QAChB,0CAAyC;AAChD,SAASC,4BAA4B,QAAQ,2CAA0C;AACvF,OAAOC,UAAU,sCAAqC;AAEtD,OAAO,MAAMC,wCAAwCP;IAQnDQ,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,CAClB;QACA,KAAK,CAACF,QAAQE;QAEd,IAAI,CAACF,MAAM,GAAGA;QACd,IAAI,CAACG,WAAW,GAAG,IAAIV,kBAAkBO,QAAQC;IACnD;IAEA,MAAgBG,UACdC,KAA4B,EACkB;QAC9C,MAAMC,WAAwC,EAAE;QAChD,KAAK,MAAMC,YAAYF,MAAO;YAC5B,MAAMG,OAAO,IAAI,CAACL,WAAW,CAACK,IAAI,CAACC,SAAS,CAACF;YAE7C,4DAA4D;YAC5D,IAAI,CAACf,gBAAgBgB,OAAO;YAE5B,6CAA6C;YAC7C,IAAIA,KAAKE,QAAQ,CAAC,OAAO;YAEzB,MAAMC,WAAW,IAAI,CAACR,WAAW,CAACQ,QAAQ,CAACF,SAAS,CAACF;YACrD,MAAMK,aAAa,IAAI,CAACT,WAAW,CAACS,UAAU,CAACH,SAAS,CAACF;YACzD,MAAMM,MAAMhB,KAAKiB,OAAO,CAACP,UAAUQ,KAAK,CAAC;YACzC,MAAMC,2BAA2BtB,oBAC/Ba,SAASU,OAAO,CAAC,IAAI,CAACjB,MAAM,EAAE,KAC9B;gBAACa;aAAI,EACL;YAGF,IAAIG,4BAA4B,CAACrB,sBAAsBa,OAAO;gBAC5D,oCAAoC;gBACpC,2DAA2D;gBAC3D;oBACE,UAAU;oBACV,oCAAoC;oBACpC,0BAA0B;oBAC1B,+CAA+C;oBAC/C,qCAAqC;oBACrC,2BAA2B;oBAC3B,MAAMU,eAAetB,6BAA6BY,MAAM;oBACxD,MAAMW,mBAAmBvB,6BAA6Be,UAAU;oBAChE,MAAMS,qBAAqBxB,6BACzBgB,YACA;oBAGF,MAAMS,UAAU,IAAIhC,qBAAqB;wBACvCiC,MAAMhC,UAAUiC,SAAS;wBACzBf,MAAMU;wBACNP,UAAUQ;wBACVP,YAAYQ;wBACZb;oBACF;oBACAD,SAASkB,IAAI,CAACH;gBAChB;gBACA;oBACE,YAAY;oBACZ,kDAAkD;oBAClD,4CAA4C;oBAC5C,+CAA+C;oBAC/C,iDAAiD;oBACjD,uCAAuC;oBACvC,MAAMH,eAAetB,6BAA6BY,MAAM;oBACxD,MAAMW,mBAAmBvB,6BAA6Be,UAAU;oBAChE,MAAMS,qBAAqBxB,6BACzBgB,YACA;oBAGF,MAAMS,UAAU,IAAIhC,qBAAqB;wBACvCiC,MAAMhC,UAAUiC,SAAS;wBACzBf,MAAMU;wBACNP,UAAUQ;wBACVP,YAAYQ;wBACZb;oBACF;oBACAD,SAASkB,IAAI,CAACH;gBAChB;YACF,OAAO;gBACL,gDAAgD;gBAChDf,SAASkB,IAAI,CACX,IAAInC,qBAAqB;oBACvBiC,MAAMhC,UAAUiC,SAAS;oBACzBf;oBACAG;oBACAC;oBACAL;gBACF;YAEJ;QACF;QAEA,OAAOD;IACT;AACF"}