{"version": 3, "sources": ["../../../../src/build/webpack/plugins/build-manifest-plugin.ts"], "sourcesContent": ["import type { <PERSON>Filter } from '../../../shared/lib/bloom-filter'\nimport type { Rewrite, CustomRoutes } from '../../../lib/load-custom-routes'\nimport devalue from 'next/dist/compiled/devalue'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  BUILD_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  SYSTEM_ENTRYPOINTS,\n} from '../../../shared/lib/constants'\nimport type { BuildManifest } from '../../../server/get-page-files'\nimport getRouteFromEntrypoint from '../../../server/get-route-from-entrypoint'\nimport { ampFirstEntryNamesMap } from './next-drop-client-page-plugin'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport { Span } from '../../../trace'\nimport { getCompilationSpan } from '../utils'\n\ntype DeepMutable<T> = { -readonly [P in keyof T]: DeepMutable<T[P]> }\n\nexport type ClientBuildManifest = {\n  [key: string]: string[]\n}\n\n// Add the runtime ssg manifest file as a lazy-loaded file dependency.\n// We also stub this file out for development mode (when it is not\n// generated).\nexport const srcEmptySsgManifest = `self.__SSG_MANIFEST=new Set;self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n// nodejs: '/static/<build id>/low-priority.js'\nfunction buildNodejsLowPriorityPath(filename: string, buildId: string) {\n  return `${CLIENT_STATIC_FILES_PATH}/${buildId}/${filename}`\n}\n\nexport function createEdgeRuntimeManifest(\n  originAssetMap: Partial<BuildManifest>\n): string {\n  const manifestFilenames = ['_buildManifest.js', '_ssgManifest.js']\n\n  const assetMap: Partial<BuildManifest> = {\n    ...originAssetMap,\n    lowPriorityFiles: [],\n  }\n\n  // we use globalThis here because middleware can be node\n  // which doesn't have \"self\"\n  const manifestDefCode = `globalThis.__BUILD_MANIFEST = ${JSON.stringify(\n    assetMap,\n    null,\n    2\n  )};\\n`\n  // edge lowPriorityFiles item: '\"/static/\" + process.env.__NEXT_BUILD_ID + \"/low-priority.js\"'.\n  // Since lowPriorityFiles is not fixed and relying on `process.env.__NEXT_BUILD_ID`, we'll produce code creating it dynamically.\n  const lowPriorityFilesCode =\n    `globalThis.__BUILD_MANIFEST.lowPriorityFiles = [\\n` +\n    manifestFilenames\n      .map((filename) => {\n        return `\"/static/\" + process.env.__NEXT_BUILD_ID + \"/${filename}\",\\n`\n      })\n      .join(',') +\n    `\\n];`\n\n  return manifestDefCode + lowPriorityFilesCode\n}\n\nfunction normalizeRewrite(item: {\n  source: string\n  destination: string\n  has?: any\n}): CustomRoutes['rewrites']['beforeFiles'][0] {\n  return {\n    has: item.has,\n    source: item.source,\n    destination: item.destination,\n  }\n}\n\nexport function normalizeRewritesForBuildManifest(\n  rewrites: CustomRoutes['rewrites']\n): CustomRoutes['rewrites'] {\n  return {\n    afterFiles: rewrites.afterFiles\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n    beforeFiles: rewrites.beforeFiles\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n    fallback: rewrites.fallback\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n  }\n}\n\n// This function takes the asset map generated in BuildManifestPlugin and creates a\n// reduced version to send to the client.\nexport function generateClientManifest(\n  assetMap: BuildManifest,\n  rewrites: CustomRoutes['rewrites'],\n  clientRouterFilters?: {\n    staticFilter: ReturnType<BloomFilter['export']>\n    dynamicFilter: ReturnType<BloomFilter['export']>\n  },\n  compiler?: any,\n  compilation?: any\n): string | undefined {\n  const compilationSpan = compilation\n    ? getCompilationSpan(compilation)\n    : compiler\n      ? getCompilationSpan(compiler)\n      : new Span({ name: 'client-manifest' })\n\n  const genClientManifestSpan = compilationSpan?.traceChild(\n    'NextJsBuildManifest-generateClientManifest'\n  )\n\n  return genClientManifestSpan?.traceFn(() => {\n    const clientManifest: ClientBuildManifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      __routerFilterStatic: clientRouterFilters?.staticFilter as any,\n      __routerFilterDynamic: clientRouterFilters?.dynamicFilter as any,\n    }\n    const appDependencies = new Set(assetMap.pages['/_app'])\n    const sortedPageKeys = getSortedRoutes(Object.keys(assetMap.pages))\n\n    sortedPageKeys.forEach((page) => {\n      const dependencies = assetMap.pages[page]\n\n      if (page === '/_app') return\n      // Filter out dependencies in the _app entry, because those will have already\n      // been loaded by the client prior to a navigation event\n      const filteredDeps = dependencies.filter(\n        (dep) => !appDependencies.has(dep)\n      )\n\n      // The manifest can omit the page if it has no requirements\n      if (filteredDeps.length) {\n        clientManifest[page] = filteredDeps\n      }\n    })\n    // provide the sorted pages as an array so we don't rely on the object's keys\n    // being in order and we don't slow down look-up time for page assets\n    clientManifest.sortedPages = sortedPageKeys\n\n    return devalue(clientManifest)\n  })\n}\n\nexport function getEntrypointFiles(entrypoint: any): string[] {\n  return (\n    entrypoint\n      ?.getFiles()\n      .filter((file: string) => {\n        // We don't want to include `.hot-update.js` files into the initial page\n        return /(?<!\\.hot-update)\\.(js|css)($|\\?)/.test(file)\n      })\n      .map((file: string) => file.replace(/\\\\/g, '/')) ?? []\n  )\n}\n\nexport const processRoute = (r: Rewrite) => {\n  const rewrite = { ...r }\n\n  // omit external rewrite destinations since these aren't\n  // handled client-side\n  if (!rewrite?.destination?.startsWith('/')) {\n    delete (rewrite as any).destination\n  }\n  return rewrite\n}\n\n// This plugin creates a build-manifest.json for all assets that are being output\n// It has a mapping of \"entry\" filename to real filename. Because the real filename can be hashed in production\nexport default class BuildManifestPlugin {\n  private buildId: string\n  private rewrites: CustomRoutes['rewrites']\n  private isDevFallback: boolean\n  private appDirEnabled: boolean\n  private clientRouterFilters?: Parameters<typeof generateClientManifest>[2]\n\n  constructor(options: {\n    buildId: string\n    rewrites: CustomRoutes['rewrites']\n    isDevFallback?: boolean\n    appDirEnabled: boolean\n    clientRouterFilters?: Parameters<typeof generateClientManifest>[2]\n  }) {\n    this.buildId = options.buildId\n    this.isDevFallback = !!options.isDevFallback\n    this.rewrites = {\n      beforeFiles: [],\n      afterFiles: [],\n      fallback: [],\n    }\n    this.appDirEnabled = options.appDirEnabled\n    this.clientRouterFilters = options.clientRouterFilters\n    this.rewrites.beforeFiles = options.rewrites.beforeFiles.map(processRoute)\n    this.rewrites.afterFiles = options.rewrites.afterFiles.map(processRoute)\n    this.rewrites.fallback = options.rewrites.fallback.map(processRoute)\n  }\n\n  createAssets(compiler: any, compilation: any) {\n    const compilationSpan =\n      getCompilationSpan(compilation) ?? getCompilationSpan(compiler)\n    if (!compilationSpan) {\n      throw new Error('No span found for compilation')\n    }\n\n    const createAssetsSpan = compilationSpan.traceChild(\n      'NextJsBuildManifest-createassets'\n    )\n\n    return createAssetsSpan.traceFn(() => {\n      const entrypoints: Map<string, any> = compilation.entrypoints\n      const assetMap: DeepMutable<BuildManifest> = {\n        polyfillFiles: [],\n        devFiles: [],\n        ampDevFiles: [],\n        lowPriorityFiles: [],\n        rootMainFiles: [],\n        rootMainFilesTree: {},\n        pages: { '/_app': [] },\n        ampFirstPages: [],\n      }\n\n      const ampFirstEntryNames = ampFirstEntryNamesMap.get(compilation)\n      if (ampFirstEntryNames) {\n        for (const entryName of ampFirstEntryNames) {\n          const pagePath = getRouteFromEntrypoint(entryName)\n          if (!pagePath) {\n            continue\n          }\n\n          assetMap.ampFirstPages.push(pagePath)\n        }\n      }\n\n      const mainFiles = new Set(\n        getEntrypointFiles(entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_MAIN))\n      )\n\n      if (this.appDirEnabled) {\n        assetMap.rootMainFiles = [\n          ...new Set(\n            getEntrypointFiles(\n              entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_MAIN_APP)\n            )\n          ),\n        ]\n      }\n\n      const compilationAssets: {\n        name: string\n        source: typeof sources.RawSource\n        info: object\n      }[] = compilation.getAssets()\n\n      assetMap.polyfillFiles = compilationAssets\n        .filter((p) => {\n          // Ensure only .js files are passed through\n          if (!p.name.endsWith('.js')) {\n            return false\n          }\n\n          return (\n            p.info && CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL in p.info\n          )\n        })\n        .map((v) => v.name)\n\n      assetMap.devFiles = getEntrypointFiles(\n        entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH)\n      ).filter((file) => !mainFiles.has(file))\n\n      assetMap.ampDevFiles = getEntrypointFiles(\n        entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_AMP)\n      )\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        if (SYSTEM_ENTRYPOINTS.has(entrypoint.name)) continue\n        const pagePath = getRouteFromEntrypoint(entrypoint.name)\n\n        if (!pagePath) {\n          continue\n        }\n\n        const filesForPage = getEntrypointFiles(entrypoint)\n\n        assetMap.pages[pagePath] = [...new Set([...mainFiles, ...filesForPage])]\n      }\n\n      if (!this.isDevFallback) {\n        // Add the runtime build manifest file (generated later in this file)\n        // as a dependency for the app. If the flag is false, the file won't be\n        // downloaded by the client.\n        const buildManifestPath = buildNodejsLowPriorityPath(\n          '_buildManifest.js',\n          this.buildId\n        )\n        const ssgManifestPath = buildNodejsLowPriorityPath(\n          '_ssgManifest.js',\n          this.buildId\n        )\n        assetMap.lowPriorityFiles.push(buildManifestPath, ssgManifestPath)\n        compilation.emitAsset(\n          ssgManifestPath,\n          new sources.RawSource(srcEmptySsgManifest)\n        )\n      }\n\n      assetMap.pages = Object.keys(assetMap.pages)\n        .sort()\n        .reduce(\n          // eslint-disable-next-line\n          (a, c) => ((a[c] = assetMap.pages[c]), a),\n          {} as typeof assetMap.pages\n        )\n\n      let buildManifestName = BUILD_MANIFEST\n\n      if (this.isDevFallback) {\n        buildManifestName = `fallback-${BUILD_MANIFEST}`\n      }\n\n      compilation.emitAsset(\n        buildManifestName,\n        new sources.RawSource(JSON.stringify(assetMap, null, 2))\n      )\n\n      compilation.emitAsset(\n        `server/${MIDDLEWARE_BUILD_MANIFEST}.js`,\n        new sources.RawSource(`${createEdgeRuntimeManifest(assetMap)}`)\n      )\n\n      if (!this.isDevFallback) {\n        compilation.emitAsset(\n          `${CLIENT_STATIC_FILES_PATH}/${this.buildId}/_buildManifest.js`,\n          new sources.RawSource(\n            `self.__BUILD_MANIFEST = ${generateClientManifest(\n              assetMap,\n              this.rewrites,\n              this.clientRouterFilters,\n              compiler,\n              compilation\n            )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n          )\n        )\n      }\n    })\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.make.tap('NextJsBuildManifest', (compilation: any) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: 'NextJsBuildManifest',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => {\n          this.createAssets(compiler, compilation)\n        }\n      )\n    })\n    return\n  }\n}\n"], "names": ["devalue", "webpack", "sources", "BUILD_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "CLIENT_STATIC_FILES_PATH", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "SYSTEM_ENTRYPOINTS", "getRouteFromEntrypoint", "ampFirstEntryNamesMap", "getSortedRoutes", "Span", "getCompilationSpan", "srcEmptySsgManifest", "buildNodejsLowPriorityPath", "filename", "buildId", "createEdgeRuntimeManifest", "originAssetMap", "manifestFilenames", "assetMap", "lowPriorityFiles", "manifestDefCode", "JSON", "stringify", "lowPriorityFilesCode", "map", "join", "normalizeRewrite", "item", "has", "source", "destination", "normalizeRewritesForBuildManifest", "rewrites", "afterFiles", "processRoute", "beforeFiles", "fallback", "generateClientManifest", "clientRouterFilters", "compiler", "compilation", "compilationSpan", "name", "genClientManifestSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "clientManifest", "__rewrites", "__routerFilterStatic", "staticFilter", "__routerFilterDynamic", "dynamicFilter", "appDependencies", "Set", "pages", "sortedPageKeys", "Object", "keys", "for<PERSON>ach", "page", "dependencies", "filteredDeps", "filter", "dep", "length", "sortedPages", "getEntrypointFiles", "entrypoint", "getFiles", "file", "test", "replace", "r", "rewrite", "startsWith", "BuildManifestPlugin", "constructor", "options", "isDev<PERSON><PERSON><PERSON>", "appDirEnabled", "createAssets", "Error", "createAssetsSpan", "entrypoints", "polyfillFiles", "devFiles", "ampDevFiles", "rootMainFiles", "rootMainFilesTree", "ampFirstPages", "ampFirstEntryNames", "get", "entryName", "pagePath", "push", "mainFiles", "compilationAssets", "getAssets", "p", "endsWith", "info", "v", "values", "filesForPage", "buildManifestPath", "ssgManifestPath", "emitAsset", "RawSource", "sort", "reduce", "a", "c", "buildManifestName", "apply", "hooks", "make", "tap", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAEA,OAAOA,aAAa,6BAA4B;AAChD,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,cAAc,EACdC,yBAAyB,EACzBC,wBAAwB,EACxBC,gCAAgC,EAChCC,oCAAoC,EACpCC,4CAA4C,EAC5CC,yCAAyC,EACzCC,+BAA+B,EAC/BC,kBAAkB,QACb,gCAA+B;AAEtC,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,IAAI,QAAQ,iBAAgB;AACrC,SAASC,kBAAkB,QAAQ,WAAU;AAQ7C,sEAAsE;AACtE,kEAAkE;AAClE,cAAc;AACd,OAAO,MAAMC,sBAAsB,CAAC,4EAA4E,CAAC,CAAA;AAEjH,+CAA+C;AAC/C,SAASC,2BAA2BC,QAAgB,EAAEC,OAAe;IACnE,OAAO,GAAGf,yBAAyB,CAAC,EAAEe,QAAQ,CAAC,EAAED,UAAU;AAC7D;AAEA,OAAO,SAASE,0BACdC,cAAsC;IAEtC,MAAMC,oBAAoB;QAAC;QAAqB;KAAkB;IAElE,MAAMC,WAAmC;QACvC,GAAGF,cAAc;QACjBG,kBAAkB,EAAE;IACtB;IAEA,wDAAwD;IACxD,4BAA4B;IAC5B,MAAMC,kBAAkB,CAAC,8BAA8B,EAAEC,KAAKC,SAAS,CACrEJ,UACA,MACA,GACA,GAAG,CAAC;IACN,+FAA+F;IAC/F,gIAAgI;IAChI,MAAMK,uBACJ,CAAC,kDAAkD,CAAC,GACpDN,kBACGO,GAAG,CAAC,CAACX;QACJ,OAAO,CAAC,6CAA6C,EAAEA,SAAS,IAAI,CAAC;IACvE,GACCY,IAAI,CAAC,OACR,CAAC,IAAI,CAAC;IAER,OAAOL,kBAAkBG;AAC3B;AAEA,SAASG,iBAAiBC,IAIzB;IACC,OAAO;QACLC,KAAKD,KAAKC,GAAG;QACbC,QAAQF,KAAKE,MAAM;QACnBC,aAAaH,KAAKG,WAAW;IAC/B;AACF;AAEA,OAAO,SAASC,kCACdC,QAAkC;QAGpBA,0BAAAA,sBAGCA,2BAAAA,uBAGHA,wBAAAA;IAPZ,OAAO;QACLC,UAAU,GAAED,uBAAAA,SAASC,UAAU,sBAAnBD,2BAAAA,qBACRR,GAAG,CAACU,kCADIF,yBAERR,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QACnCQ,WAAW,GAAEH,wBAAAA,SAASG,WAAW,sBAApBH,4BAAAA,sBACTR,GAAG,CAACU,kCADKF,0BAETR,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QACnCS,QAAQ,GAAEJ,qBAAAA,SAASI,QAAQ,sBAAjBJ,yBAAAA,mBACNR,GAAG,CAACU,kCADEF,uBAENR,GAAG,CAAC,CAACG,OAASD,iBAAiBC;IACrC;AACF;AAEA,mFAAmF;AACnF,yCAAyC;AACzC,OAAO,SAASU,uBACdnB,QAAuB,EACvBc,QAAkC,EAClCM,mBAGC,EACDC,QAAc,EACdC,WAAiB;IAEjB,MAAMC,kBAAkBD,cACpB9B,mBAAmB8B,eACnBD,WACE7B,mBAAmB6B,YACnB,IAAI9B,KAAK;QAAEiC,MAAM;IAAkB;IAEzC,MAAMC,wBAAwBF,mCAAAA,gBAAiBG,UAAU,CACvD;IAGF,OAAOD,yCAAAA,sBAAuBE,OAAO,CAAC;QACpC,MAAMC,iBAAsC;YAC1CC,YAAYhB,kCAAkCC;YAC9CgB,oBAAoB,EAAEV,uCAAAA,oBAAqBW,YAAY;YACvDC,qBAAqB,EAAEZ,uCAAAA,oBAAqBa,aAAa;QAC3D;QACA,MAAMC,kBAAkB,IAAIC,IAAInC,SAASoC,KAAK,CAAC,QAAQ;QACvD,MAAMC,iBAAiB/C,gBAAgBgD,OAAOC,IAAI,CAACvC,SAASoC,KAAK;QAEjEC,eAAeG,OAAO,CAAC,CAACC;YACtB,MAAMC,eAAe1C,SAASoC,KAAK,CAACK,KAAK;YAEzC,IAAIA,SAAS,SAAS;YACtB,6EAA6E;YAC7E,wDAAwD;YACxD,MAAME,eAAeD,aAAaE,MAAM,CACtC,CAACC,MAAQ,CAACX,gBAAgBxB,GAAG,CAACmC;YAGhC,2DAA2D;YAC3D,IAAIF,aAAaG,MAAM,EAAE;gBACvBlB,cAAc,CAACa,KAAK,GAAGE;YACzB;QACF;QACA,6EAA6E;QAC7E,qEAAqE;QACrEf,eAAemB,WAAW,GAAGV;QAE7B,OAAO7D,QAAQoD;IACjB;AACF;AAEA,OAAO,SAASoB,mBAAmBC,UAAe;IAChD,OACEA,CAAAA,8BAAAA,WACIC,QAAQ,GACTN,MAAM,CAAC,CAACO;QACP,wEAAwE;QACxE,OAAO,oCAAoCC,IAAI,CAACD;IAClD,GACC7C,GAAG,CAAC,CAAC6C,OAAiBA,KAAKE,OAAO,CAAC,OAAO,UAAS,EAAE;AAE5D;AAEA,OAAO,MAAMrC,eAAe,CAACsC;QAKtBC;IAJL,MAAMA,UAAU;QAAE,GAAGD,CAAC;IAAC;IAEvB,wDAAwD;IACxD,sBAAsB;IACtB,IAAI,EAACC,4BAAAA,uBAAAA,QAAS3C,WAAW,qBAApB2C,qBAAsBC,UAAU,CAAC,OAAM;QAC1C,OAAO,AAACD,QAAgB3C,WAAW;IACrC;IACA,OAAO2C;AACT,EAAC;AAED,iFAAiF;AACjF,+GAA+G;AAC/G,eAAe,MAAME;IAOnBC,YAAYC,OAMX,CAAE;QACD,IAAI,CAAC/D,OAAO,GAAG+D,QAAQ/D,OAAO;QAC9B,IAAI,CAACgE,aAAa,GAAG,CAAC,CAACD,QAAQC,aAAa;QAC5C,IAAI,CAAC9C,QAAQ,GAAG;YACdG,aAAa,EAAE;YACfF,YAAY,EAAE;YACdG,UAAU,EAAE;QACd;QACA,IAAI,CAAC2C,aAAa,GAAGF,QAAQE,aAAa;QAC1C,IAAI,CAACzC,mBAAmB,GAAGuC,QAAQvC,mBAAmB;QACtD,IAAI,CAACN,QAAQ,CAACG,WAAW,GAAG0C,QAAQ7C,QAAQ,CAACG,WAAW,CAACX,GAAG,CAACU;QAC7D,IAAI,CAACF,QAAQ,CAACC,UAAU,GAAG4C,QAAQ7C,QAAQ,CAACC,UAAU,CAACT,GAAG,CAACU;QAC3D,IAAI,CAACF,QAAQ,CAACI,QAAQ,GAAGyC,QAAQ7C,QAAQ,CAACI,QAAQ,CAACZ,GAAG,CAACU;IACzD;IAEA8C,aAAazC,QAAa,EAAEC,WAAgB,EAAE;QAC5C,MAAMC,kBACJ/B,mBAAmB8B,gBAAgB9B,mBAAmB6B;QACxD,IAAI,CAACE,iBAAiB;YACpB,MAAM,qBAA0C,CAA1C,IAAIwC,MAAM,kCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QACjD;QAEA,MAAMC,mBAAmBzC,gBAAgBG,UAAU,CACjD;QAGF,OAAOsC,iBAAiBrC,OAAO,CAAC;YAC9B,MAAMsC,cAAgC3C,YAAY2C,WAAW;YAC7D,MAAMjE,WAAuC;gBAC3CkE,eAAe,EAAE;gBACjBC,UAAU,EAAE;gBACZC,aAAa,EAAE;gBACfnE,kBAAkB,EAAE;gBACpBoE,eAAe,EAAE;gBACjBC,mBAAmB,CAAC;gBACpBlC,OAAO;oBAAE,SAAS,EAAE;gBAAC;gBACrBmC,eAAe,EAAE;YACnB;YAEA,MAAMC,qBAAqBnF,sBAAsBoF,GAAG,CAACnD;YACrD,IAAIkD,oBAAoB;gBACtB,KAAK,MAAME,aAAaF,mBAAoB;oBAC1C,MAAMG,WAAWvF,uBAAuBsF;oBACxC,IAAI,CAACC,UAAU;wBACb;oBACF;oBAEA3E,SAASuE,aAAa,CAACK,IAAI,CAACD;gBAC9B;YACF;YAEA,MAAME,YAAY,IAAI1C,IACpBa,mBAAmBiB,YAAYQ,GAAG,CAAC3F;YAGrC,IAAI,IAAI,CAAC+E,aAAa,EAAE;gBACtB7D,SAASqE,aAAa,GAAG;uBACpB,IAAIlC,IACLa,mBACEiB,YAAYQ,GAAG,CAAC1F;iBAGrB;YACH;YAEA,MAAM+F,oBAIAxD,YAAYyD,SAAS;YAE3B/E,SAASkE,aAAa,GAAGY,kBACtBlC,MAAM,CAAC,CAACoC;gBACP,2CAA2C;gBAC3C,IAAI,CAACA,EAAExD,IAAI,CAACyD,QAAQ,CAAC,QAAQ;oBAC3B,OAAO;gBACT;gBAEA,OACED,EAAEE,IAAI,IAAIlG,gDAAgDgG,EAAEE,IAAI;YAEpE,GACC5E,GAAG,CAAC,CAAC6E,IAAMA,EAAE3D,IAAI;YAEpBxB,SAASmE,QAAQ,GAAGnB,mBAClBiB,YAAYQ,GAAG,CAACxF,4CAChB2D,MAAM,CAAC,CAACO,OAAS,CAAC0B,UAAUnE,GAAG,CAACyC;YAElCnD,SAASoE,WAAW,GAAGpB,mBACrBiB,YAAYQ,GAAG,CAACvF;YAGlB,KAAK,MAAM+D,cAAc3B,YAAY2C,WAAW,CAACmB,MAAM,GAAI;gBACzD,IAAIjG,mBAAmBuB,GAAG,CAACuC,WAAWzB,IAAI,GAAG;gBAC7C,MAAMmD,WAAWvF,uBAAuB6D,WAAWzB,IAAI;gBAEvD,IAAI,CAACmD,UAAU;oBACb;gBACF;gBAEA,MAAMU,eAAerC,mBAAmBC;gBAExCjD,SAASoC,KAAK,CAACuC,SAAS,GAAG;uBAAI,IAAIxC,IAAI;2BAAI0C;2BAAcQ;qBAAa;iBAAE;YAC1E;YAEA,IAAI,CAAC,IAAI,CAACzB,aAAa,EAAE;gBACvB,qEAAqE;gBACrE,uEAAuE;gBACvE,4BAA4B;gBAC5B,MAAM0B,oBAAoB5F,2BACxB,qBACA,IAAI,CAACE,OAAO;gBAEd,MAAM2F,kBAAkB7F,2BACtB,mBACA,IAAI,CAACE,OAAO;gBAEdI,SAASC,gBAAgB,CAAC2E,IAAI,CAACU,mBAAmBC;gBAClDjE,YAAYkE,SAAS,CACnBD,iBACA,IAAI7G,QAAQ+G,SAAS,CAAChG;YAE1B;YAEAO,SAASoC,KAAK,GAAGE,OAAOC,IAAI,CAACvC,SAASoC,KAAK,EACxCsD,IAAI,GACJC,MAAM,CACL,2BAA2B;YAC3B,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAG7F,SAASoC,KAAK,CAACyD,EAAE,EAAGD,CAAAA,GACvC,CAAC;YAGL,IAAIE,oBAAoBnH;YAExB,IAAI,IAAI,CAACiF,aAAa,EAAE;gBACtBkC,oBAAoB,CAAC,SAAS,EAAEnH,gBAAgB;YAClD;YAEA2C,YAAYkE,SAAS,CACnBM,mBACA,IAAIpH,QAAQ+G,SAAS,CAACtF,KAAKC,SAAS,CAACJ,UAAU,MAAM;YAGvDsB,YAAYkE,SAAS,CACnB,CAAC,OAAO,EAAE5G,0BAA0B,GAAG,CAAC,EACxC,IAAIF,QAAQ+G,SAAS,CAAC,GAAG5F,0BAA0BG,WAAW;YAGhE,IAAI,CAAC,IAAI,CAAC4D,aAAa,EAAE;gBACvBtC,YAAYkE,SAAS,CACnB,GAAG3G,yBAAyB,CAAC,EAAE,IAAI,CAACe,OAAO,CAAC,kBAAkB,CAAC,EAC/D,IAAIlB,QAAQ+G,SAAS,CACnB,CAAC,wBAAwB,EAAEtE,uBACzBnB,UACA,IAAI,CAACc,QAAQ,EACb,IAAI,CAACM,mBAAmB,EACxBC,UACAC,aACA,uDAAuD,CAAC;YAGhE;QACF;IACF;IAEAyE,MAAM1E,QAA0B,EAAE;QAChCA,SAAS2E,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAAC5E;YAC9CA,YAAY0E,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACE1E,MAAM;gBACN4E,OAAO3H,QAAQ4H,WAAW,CAACC,8BAA8B;YAC3D,GACA;gBACE,IAAI,CAACxC,YAAY,CAACzC,UAAUC;YAC9B;QAEJ;QACA;IACF;AACF"}