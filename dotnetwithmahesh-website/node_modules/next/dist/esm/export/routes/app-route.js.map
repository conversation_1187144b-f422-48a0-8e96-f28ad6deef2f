{"version": 3, "sources": ["../../../src/export/routes/app-route.ts"], "sourcesContent": ["import type { ExportRouteResult } from '../types'\nimport type AppRouteRouteModule from '../../server/route-modules/app-route/module'\nimport type { AppRouteRouteHandlerContext } from '../../server/route-modules/app-route/module'\nimport type { IncrementalCache } from '../../server/lib/incremental-cache'\n\nimport {\n  INFINITE_CACHE,\n  NEXT_BODY_SUFFIX,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_META_SUFFIX,\n} from '../../lib/constants'\nimport { NodeNextRequest } from '../../server/base-http/node'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from '../../server/web/spec-extension/adapters/next-request'\nimport { toNodeOutgoingHttpHeaders } from '../../server/web/utils'\nimport type {\n  MockedRequest,\n  MockedResponse,\n} from '../../server/lib/mock-request'\nimport { isDynamicUsageError } from '../helpers/is-dynamic-usage-error'\nimport { hasNextSupport } from '../../server/ci-info'\nimport { isStaticGenEnabled } from '../../server/route-modules/app-route/helpers/is-static-gen-enabled'\nimport type { ExperimentalConfig } from '../../server/config-shared'\nimport { isMetadataRoute } from '../../lib/metadata/is-metadata-route'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport type { Params } from '../../server/request/params'\nimport { AfterRunner } from '../../server/after/run-with-after'\nimport type { MultiFileWriter } from '../../lib/multi-file-writer'\n\nexport const enum ExportedAppRouteFiles {\n  BODY = 'BODY',\n  META = 'META',\n}\n\nexport async function exportAppRoute(\n  req: MockedRequest,\n  res: MockedResponse,\n  params: Params | undefined,\n  page: string,\n  module: AppRouteRouteModule,\n  incrementalCache: IncrementalCache | undefined,\n  cacheLifeProfiles:\n    | undefined\n    | {\n        [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n      },\n  htmlFilepath: string,\n  fileWriter: MultiFileWriter,\n  experimental: Required<\n    Pick<ExperimentalConfig, 'dynamicIO' | 'authInterrupts'>\n  >,\n  buildId: string\n): Promise<ExportRouteResult> {\n  // Ensure that the URL is absolute.\n  req.url = `http://localhost:3000${req.url}`\n\n  // Adapt the request and response to the Next.js request and response.\n  const request = NextRequestAdapter.fromNodeNextRequest(\n    new NodeNextRequest(req),\n    signalFromNodeResponse(res)\n  )\n\n  const afterRunner = new AfterRunner()\n\n  // Create the context for the handler. This contains the params from\n  // the route and the context for the request.\n  const context: AppRouteRouteHandlerContext = {\n    params,\n    prerenderManifest: {\n      version: 4,\n      routes: {},\n      dynamicRoutes: {},\n      preview: {\n        previewModeEncryptionKey: '',\n        previewModeId: '',\n        previewModeSigningKey: '',\n      },\n      notFoundRoutes: [],\n    },\n    renderOpts: {\n      experimental,\n      nextExport: true,\n      supportsDynamicResponse: false,\n      incrementalCache,\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n      cacheLifeProfiles,\n    },\n    sharedContext: {\n      buildId,\n    },\n  }\n\n  if (hasNextSupport) {\n    context.renderOpts.isRevalidate = true\n  }\n\n  try {\n    const userland = module.userland\n    // we don't bail from the static optimization for\n    // metadata routes, since it's app-route we can always append /route suffix.\n    const routePath = normalizeAppPath(page) + '/route'\n    const isPageMetadataRoute = isMetadataRoute(routePath)\n\n    if (\n      !isStaticGenEnabled(userland) &&\n      !isPageMetadataRoute &&\n      // We don't disable static gen when dynamicIO is enabled because we\n      // expect that anything dynamic in the GET handler will make it dynamic\n      // and thus avoid the cache surprises that led to us removing static gen\n      // unless specifically opted into\n      experimental.dynamicIO !== true\n    ) {\n      return { cacheControl: { revalidate: 0, expire: undefined } }\n    }\n\n    const response = await module.handle(request, context)\n\n    const isValidStatus = response.status < 400 || response.status === 404\n    if (!isValidStatus) {\n      return { cacheControl: { revalidate: 0, expire: undefined } }\n    }\n\n    const blob = await response.blob()\n\n    // TODO(after): if we abort a prerender because of an error in an after-callback\n    // we should probably communicate that better (and not log the error twice)\n    await afterRunner.executeAfter()\n\n    const revalidate =\n      typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n      context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n        ? false\n        : context.renderOpts.collectedRevalidate\n\n    const expire =\n      typeof context.renderOpts.collectedExpire === 'undefined' ||\n      context.renderOpts.collectedExpire >= INFINITE_CACHE\n        ? undefined\n        : context.renderOpts.collectedExpire\n\n    const headers = toNodeOutgoingHttpHeaders(response.headers)\n    const cacheTags = context.renderOpts.collectedTags\n\n    if (cacheTags) {\n      headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n    }\n\n    if (!headers['content-type'] && blob.type) {\n      headers['content-type'] = blob.type\n    }\n\n    // Writing response body to a file.\n    const body = Buffer.from(await blob.arrayBuffer())\n    fileWriter.append(htmlFilepath.replace(/\\.html$/, NEXT_BODY_SUFFIX), body)\n\n    // Write the request metadata to a file.\n    const meta = { status: response.status, headers }\n    fileWriter.append(\n      htmlFilepath.replace(/\\.html$/, NEXT_META_SUFFIX),\n      JSON.stringify(meta)\n    )\n\n    return {\n      cacheControl: { revalidate, expire },\n      metadata: meta,\n    }\n  } catch (err) {\n    if (!isDynamicUsageError(err)) {\n      throw err\n    }\n\n    return { cacheControl: { revalidate: 0, expire: undefined } }\n  }\n}\n"], "names": ["INFINITE_CACHE", "NEXT_BODY_SUFFIX", "NEXT_CACHE_TAGS_HEADER", "NEXT_META_SUFFIX", "NodeNextRequest", "NextRequestAdapter", "signalFromNodeResponse", "toNodeOutgoingHttpHeaders", "isDynamicUsageError", "hasNextSupport", "isStaticGenEnabled", "isMetadataRoute", "normalizeAppPath", "After<PERSON><PERSON>ner", "ExportedAppRouteFiles", "exportAppRoute", "req", "res", "params", "page", "module", "incrementalCache", "cacheLifeProfiles", "htmlFilepath", "fileWriter", "experimental", "buildId", "url", "request", "fromNodeNextRequest", "after<PERSON><PERSON>ner", "context", "prerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "renderOpts", "nextExport", "supportsDynamicResponse", "waitUntil", "onClose", "onAfterTaskError", "onTaskError", "sharedContext", "isRevalidate", "userland", "routePath", "isPageMetadataRoute", "dynamicIO", "cacheControl", "revalidate", "expire", "undefined", "response", "handle", "isValidStatus", "status", "blob", "executeAfter", "collectedRevalidate", "collectedExpire", "headers", "cacheTags", "collectedTags", "type", "body", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "append", "replace", "meta", "JSON", "stringify", "metadata", "err"], "mappings": "AAKA,SACEA,cAAc,EACdC,gBAAgB,EAChBC,sBAAsB,EACtBC,gBAAgB,QACX,sBAAqB;AAC5B,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SACEC,kBAAkB,EAClBC,sBAAsB,QACjB,wDAAuD;AAC9D,SAASC,yBAAyB,QAAQ,yBAAwB;AAKlE,SAASC,mBAAmB,QAAQ,oCAAmC;AACvE,SAASC,cAAc,QAAQ,uBAAsB;AACrD,SAASC,kBAAkB,QAAQ,qEAAoE;AAEvG,SAASC,eAAe,QAAQ,uCAAsC;AACtE,SAASC,gBAAgB,QAAQ,0CAAyC;AAE1E,SAASC,WAAW,QAAQ,oCAAmC;AAG/D,OAAO,IAAA,AAAWC,+CAAAA;;;WAAAA;MAGjB;AAED,OAAO,eAAeC,eACpBC,GAAkB,EAClBC,GAAmB,EACnBC,MAA0B,EAC1BC,IAAY,EACZC,MAA2B,EAC3BC,gBAA8C,EAC9CC,iBAIK,EACLC,YAAoB,EACpBC,UAA2B,EAC3BC,YAEC,EACDC,OAAe;IAEf,mCAAmC;IACnCV,IAAIW,GAAG,GAAG,CAAC,qBAAqB,EAAEX,IAAIW,GAAG,EAAE;IAE3C,sEAAsE;IACtE,MAAMC,UAAUvB,mBAAmBwB,mBAAmB,CACpD,IAAIzB,gBAAgBY,MACpBV,uBAAuBW;IAGzB,MAAMa,cAAc,IAAIjB;IAExB,oEAAoE;IACpE,6CAA6C;IAC7C,MAAMkB,UAAuC;QAC3Cb;QACAc,mBAAmB;YACjBC,SAAS;YACTC,QAAQ,CAAC;YACTC,eAAe,CAAC;YAChBC,SAAS;gBACPC,0BAA0B;gBAC1BC,eAAe;gBACfC,uBAAuB;YACzB;YACAC,gBAAgB,EAAE;QACpB;QACAC,YAAY;YACVhB;YACAiB,YAAY;YACZC,yBAAyB;YACzBtB;YACAuB,WAAWd,YAAYC,OAAO,CAACa,SAAS;YACxCC,SAASf,YAAYC,OAAO,CAACc,OAAO;YACpCC,kBAAkBhB,YAAYC,OAAO,CAACgB,WAAW;YACjDzB;QACF;QACA0B,eAAe;YACbtB;QACF;IACF;IAEA,IAAIjB,gBAAgB;QAClBsB,QAAQU,UAAU,CAACQ,YAAY,GAAG;IACpC;IAEA,IAAI;QACF,MAAMC,WAAW9B,OAAO8B,QAAQ;QAChC,iDAAiD;QACjD,4EAA4E;QAC5E,MAAMC,YAAYvC,iBAAiBO,QAAQ;QAC3C,MAAMiC,sBAAsBzC,gBAAgBwC;QAE5C,IACE,CAACzC,mBAAmBwC,aACpB,CAACE,uBACD,mEAAmE;QACnE,uEAAuE;QACvE,wEAAwE;QACxE,iCAAiC;QACjC3B,aAAa4B,SAAS,KAAK,MAC3B;YACA,OAAO;gBAAEC,cAAc;oBAAEC,YAAY;oBAAGC,QAAQC;gBAAU;YAAE;QAC9D;QAEA,MAAMC,WAAW,MAAMtC,OAAOuC,MAAM,CAAC/B,SAASG;QAE9C,MAAM6B,gBAAgBF,SAASG,MAAM,GAAG,OAAOH,SAASG,MAAM,KAAK;QACnE,IAAI,CAACD,eAAe;YAClB,OAAO;gBAAEN,cAAc;oBAAEC,YAAY;oBAAGC,QAAQC;gBAAU;YAAE;QAC9D;QAEA,MAAMK,OAAO,MAAMJ,SAASI,IAAI;QAEhC,gFAAgF;QAChF,2EAA2E;QAC3E,MAAMhC,YAAYiC,YAAY;QAE9B,MAAMR,aACJ,OAAOxB,QAAQU,UAAU,CAACuB,mBAAmB,KAAK,eAClDjC,QAAQU,UAAU,CAACuB,mBAAmB,IAAIhE,iBACtC,QACA+B,QAAQU,UAAU,CAACuB,mBAAmB;QAE5C,MAAMR,SACJ,OAAOzB,QAAQU,UAAU,CAACwB,eAAe,KAAK,eAC9ClC,QAAQU,UAAU,CAACwB,eAAe,IAAIjE,iBAClCyD,YACA1B,QAAQU,UAAU,CAACwB,eAAe;QAExC,MAAMC,UAAU3D,0BAA0BmD,SAASQ,OAAO;QAC1D,MAAMC,YAAYpC,QAAQU,UAAU,CAAC2B,aAAa;QAElD,IAAID,WAAW;YACbD,OAAO,CAAChE,uBAAuB,GAAGiE;QACpC;QAEA,IAAI,CAACD,OAAO,CAAC,eAAe,IAAIJ,KAAKO,IAAI,EAAE;YACzCH,OAAO,CAAC,eAAe,GAAGJ,KAAKO,IAAI;QACrC;QAEA,mCAAmC;QACnC,MAAMC,OAAOC,OAAOC,IAAI,CAAC,MAAMV,KAAKW,WAAW;QAC/CjD,WAAWkD,MAAM,CAACnD,aAAaoD,OAAO,CAAC,WAAW1E,mBAAmBqE;QAErE,wCAAwC;QACxC,MAAMM,OAAO;YAAEf,QAAQH,SAASG,MAAM;YAAEK;QAAQ;QAChD1C,WAAWkD,MAAM,CACfnD,aAAaoD,OAAO,CAAC,WAAWxE,mBAChC0E,KAAKC,SAAS,CAACF;QAGjB,OAAO;YACLtB,cAAc;gBAAEC;gBAAYC;YAAO;YACnCuB,UAAUH;QACZ;IACF,EAAE,OAAOI,KAAK;QACZ,IAAI,CAACxE,oBAAoBwE,MAAM;YAC7B,MAAMA;QACR;QAEA,OAAO;YAAE1B,cAAc;gBAAEC,YAAY;gBAAGC,QAAQC;YAAU;QAAE;IAC9D;AACF"}