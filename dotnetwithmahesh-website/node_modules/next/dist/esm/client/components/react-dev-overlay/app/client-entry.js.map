{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/client-entry.tsx"], "sourcesContent": ["import React from 'react'\nimport { getSocketUrl } from '../utils/get-socket-url'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport GlobalError from '../../error-boundary'\nimport { AppDevOverlayErrorBoundary } from './app-dev-overlay-error-boundary'\n\nconst noop = () => {}\n\n// if an error is thrown while rendering an RSC stream, this will catch it in dev\n// and show the error overlay\nexport function createRootLevelDevOverlayElement(reactEl: React.ReactElement) {\n  const socketUrl = getSocketUrl(process.env.__NEXT_ASSET_PREFIX || '')\n  const socket = new window.WebSocket(`${socketUrl}/_next/webpack-hmr`)\n\n  // add minimal \"hot reload\" support for RSC errors\n  const handler = (event: MessageEvent) => {\n    let obj\n    try {\n      obj = JSON.parse(event.data)\n    } catch {}\n\n    if (!obj || !('action' in obj)) {\n      return\n    }\n\n    if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES) {\n      window.location.reload()\n    }\n  }\n\n  socket.addEventListener('message', handler)\n\n  return (\n    <AppDevOverlayErrorBoundary\n      globalError={[GlobalError, null]}\n      onError={noop}\n    >\n      {reactEl}\n    </AppDevOverlayErrorBoundary>\n  )\n}\n"], "names": ["React", "getSocketUrl", "HMR_ACTIONS_SENT_TO_BROWSER", "GlobalError", "AppDevOverlayErrorBoundary", "noop", "createRootLevelDevOverlayElement", "reactEl", "socketUrl", "process", "env", "__NEXT_ASSET_PREFIX", "socket", "window", "WebSocket", "handler", "event", "obj", "JSON", "parse", "data", "action", "SERVER_COMPONENT_CHANGES", "location", "reload", "addEventListener", "globalError", "onError"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,2BAA2B,QAAQ,4CAA2C;AACvF,OAAOC,iBAAiB,uBAAsB;AAC9C,SAASC,0BAA0B,QAAQ,mCAAkC;AAE7E,MAAMC,OAAO,KAAO;AAEpB,iFAAiF;AACjF,6BAA6B;AAC7B,OAAO,SAASC,iCAAiCC,OAA2B;IAC1E,MAAMC,YAAYP,aAAaQ,QAAQC,GAAG,CAACC,mBAAmB,IAAI;IAClE,MAAMC,SAAS,IAAIC,OAAOC,SAAS,CAAC,AAAC,KAAEN,YAAU;IAEjD,kDAAkD;IAClD,MAAMO,UAAU,CAACC;QACf,IAAIC;QACJ,IAAI;YACFA,MAAMC,KAAKC,KAAK,CAACH,MAAMI,IAAI;QAC7B,EAAE,UAAM,CAAC;QAET,IAAI,CAACH,OAAO,CAAE,CAAA,YAAYA,GAAE,GAAI;YAC9B;QACF;QAEA,IAAIA,IAAII,MAAM,KAAKnB,4BAA4BoB,wBAAwB,EAAE;YACvET,OAAOU,QAAQ,CAACC,MAAM;QACxB;IACF;IAEAZ,OAAOa,gBAAgB,CAAC,WAAWV;IAEnC,qBACE,KAACX;QACCsB,aAAa;YAACvB;YAAa;SAAK;QAChCwB,SAAStB;kBAERE;;AAGP"}