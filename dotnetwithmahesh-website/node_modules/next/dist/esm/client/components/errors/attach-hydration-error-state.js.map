{"version": 3, "sources": ["../../../../src/client/components/errors/attach-hydration-error-state.ts"], "sourcesContent": ["import {\n  getDefaultHydrationErrorMessage,\n  isHydrationError,\n  testReactHydrationWarning,\n} from '../is-hydration-error'\nimport {\n  hydrationErrorState,\n  getReactHydrationDiffSegments,\n  type HydrationErrorState,\n} from './hydration-error-info'\n\nexport function attachHydrationErrorState(error: Error) {\n  let parsedHydrationErrorState: typeof hydrationErrorState = {}\n  const isHydrationWarning = testReactHydrationWarning(error.message)\n  const isHydrationRuntimeError = isHydrationError(error)\n\n  // If it's not hydration warnings or errors, skip\n  if (!(isHydrationRuntimeError || isHydrationWarning)) {\n    return\n  }\n\n  const reactHydrationDiffSegments = getReactHydrationDiffSegments(\n    error.message\n  )\n  // If the reactHydrationDiffSegments exists\n  // and the diff (reactHydrationDiffSegments[1]) exists\n  // e.g. the hydration diff log error.\n  if (reactHydrationDiffSegments) {\n    const diff = reactHydrationDiffSegments[1]\n    parsedHydrationErrorState = {\n      ...((error as any).details as HydrationErrorState),\n      ...hydrationErrorState,\n      // If diff is present in error, we don't need to pick up the console logged warning.\n      // - if hydration error has diff, and is not hydration diff log, then it's a normal hydration error.\n      // - if hydration error no diff, then leverage the one from the hydration diff log.\n\n      warning: (diff && !isHydrationWarning\n        ? null\n        : hydrationErrorState.warning) || [\n        getDefaultHydrationErrorMessage(),\n        '',\n        '',\n      ],\n      // When it's hydration diff log, do not show notes section.\n      // This condition is only for the 1st squashed error.\n      notes: isHydrationWarning ? '' : reactHydrationDiffSegments[0],\n      reactOutputComponentDiff: diff,\n    }\n    // Cache the `reactOutputComponentDiff` into hydrationErrorState.\n    // This is only required for now when we still squashed the hydration diff log into hydration error.\n    // Once the all error is logged to dev overlay in order, this will go away.\n    if (!hydrationErrorState.reactOutputComponentDiff && diff) {\n      hydrationErrorState.reactOutputComponentDiff = diff\n    }\n    // If it's hydration runtime error that doesn't contain the diff, combine the diff from the cached hydration diff.\n    if (\n      !diff &&\n      isHydrationRuntimeError &&\n      hydrationErrorState.reactOutputComponentDiff\n    ) {\n      parsedHydrationErrorState.reactOutputComponentDiff =\n        hydrationErrorState.reactOutputComponentDiff\n    }\n  } else {\n    // Normal runtime error, where it doesn't contain the hydration diff.\n\n    // If there's any extra information in the error message to display,\n    // append it to the error message details property\n    if (hydrationErrorState.warning) {\n      // The patched console.error found hydration errors logged by React\n      // Append the logged warning to the error message\n      parsedHydrationErrorState = {\n        ...(error as any).details,\n        // It contains the warning, component stack, server and client tag names\n        ...hydrationErrorState,\n      }\n    }\n    // Consume the cached hydration diff.\n    // This is only required for now when we still squashed the hydration diff log into hydration error.\n    // Once the all error is logged to dev overlay in order, this will go away.\n    if (hydrationErrorState.reactOutputComponentDiff) {\n      parsedHydrationErrorState.reactOutputComponentDiff =\n        hydrationErrorState.reactOutputComponentDiff\n    }\n  }\n  // If it's a hydration error, store the hydration error state into the error object\n  ;(error as any).details = parsedHydrationErrorState\n}\n"], "names": ["getDefaultHydrationErrorMessage", "isHydrationError", "testReactHydrationWarning", "hydrationErrorState", "getReactHydrationDiffSegments", "attachHydrationErrorState", "error", "parsedHydrationErrorState", "isHydrationWarning", "message", "isHydrationRuntimeError", "reactHydrationDiffSegments", "diff", "details", "warning", "notes", "reactOutputComponentDiff"], "mappings": "AAAA,SACEA,+BAA+B,EAC/BC,gBAAgB,EAChBC,yBAAyB,QACpB,wBAAuB;AAC9B,SACEC,mBAAmB,EACnBC,6BAA6B,QAExB,yBAAwB;AAE/B,OAAO,SAASC,0BAA0BC,KAAY;IACpD,IAAIC,4BAAwD,CAAC;IAC7D,MAAMC,qBAAqBN,0BAA0BI,MAAMG,OAAO;IAClE,MAAMC,0BAA0BT,iBAAiBK;IAEjD,iDAAiD;IACjD,IAAI,CAAEI,CAAAA,2BAA2BF,kBAAiB,GAAI;QACpD;IACF;IAEA,MAAMG,6BAA6BP,8BACjCE,MAAMG,OAAO;IAEf,2CAA2C;IAC3C,sDAAsD;IACtD,qCAAqC;IACrC,IAAIE,4BAA4B;QAC9B,MAAMC,OAAOD,0BAA0B,CAAC,EAAE;QAC1CJ,4BAA4B;YAC1B,GAAI,AAACD,MAAcO,OAAO;YAC1B,GAAGV,mBAAmB;YACtB,oFAAoF;YACpF,oGAAoG;YACpG,mFAAmF;YAEnFW,SAAS,AAACF,CAAAA,QAAQ,CAACJ,qBACf,OACAL,oBAAoBW,OAAO,AAAD,KAAM;gBAClCd;gBACA;gBACA;aACD;YACD,2DAA2D;YAC3D,qDAAqD;YACrDe,OAAOP,qBAAqB,KAAKG,0BAA0B,CAAC,EAAE;YAC9DK,0BAA0BJ;QAC5B;QACA,iEAAiE;QACjE,oGAAoG;QACpG,2EAA2E;QAC3E,IAAI,CAACT,oBAAoBa,wBAAwB,IAAIJ,MAAM;YACzDT,oBAAoBa,wBAAwB,GAAGJ;QACjD;QACA,kHAAkH;QAClH,IACE,CAACA,QACDF,2BACAP,oBAAoBa,wBAAwB,EAC5C;YACAT,0BAA0BS,wBAAwB,GAChDb,oBAAoBa,wBAAwB;QAChD;IACF,OAAO;QACL,qEAAqE;QAErE,oEAAoE;QACpE,kDAAkD;QAClD,IAAIb,oBAAoBW,OAAO,EAAE;YAC/B,mEAAmE;YACnE,iDAAiD;YACjDP,4BAA4B;gBAC1B,GAAG,AAACD,MAAcO,OAAO;gBACzB,wEAAwE;gBACxE,GAAGV,mBAAmB;YACxB;QACF;QACA,qCAAqC;QACrC,oGAAoG;QACpG,2EAA2E;QAC3E,IAAIA,oBAAoBa,wBAAwB,EAAE;YAChDT,0BAA0BS,wBAAwB,GAChDb,oBAAoBa,wBAAwB;QAChD;IACF;IACA,mFAAmF;;IACjFV,MAAcO,OAAO,GAAGN;AAC5B"}