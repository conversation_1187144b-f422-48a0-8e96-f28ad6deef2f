{"version": 3, "sources": ["../../../src/server/app-render/action-async-storage-instance.ts"], "sourcesContent": ["import type { ActionAsyncStorage } from './action-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const actionAsyncStorageInstance: ActionAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["actionAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,6BACXC,IAAAA,0CAAuB"}