{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.tsx"], "sourcesContent": ["import { CopyButton } from '../../copy-button'\n\nexport function CopyStackTraceButton({ error }: { error: E<PERSON>r }) {\n  return (\n    <CopyButton\n      data-nextjs-data-runtime-error-copy-stack\n      className=\"copy-stack-trace-button\"\n      actionLabel=\"Copy Stack Trace\"\n      successLabel=\"Stack Trace Copied\"\n      content={error.stack || ''}\n      disabled={!error.stack}\n    />\n  )\n}\n"], "names": ["CopyStackTraceButton", "error", "Copy<PERSON><PERSON><PERSON>", "data-nextjs-data-runtime-error-copy-stack", "className", "actionLabel", "successLabel", "content", "stack", "disabled"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;;4BAFW;AAEpB,SAASA,qBAAqB,KAA2B;IAA3B,IAAA,EAAEC,KAAK,EAAoB,GAA3B;IACnC,qBACE,qBAACC,sBAAU;QACTC,2CAAyC;QACzCC,WAAU;QACVC,aAAY;QACZC,cAAa;QACbC,SAASN,MAAMO,KAAK,IAAI;QACxBC,UAAU,CAACR,MAAMO,KAAK;;AAG5B"}