{"version": 3, "sources": ["../../../../src/build/webpack/plugins/devtools-ignore-list-plugin.ts"], "sourcesContent": ["// Source: https://github.com/mondaychen/devtools-ignore-webpack-plugin/blob/e35ce41d9606a92a455ef247f509a1c2ccab5778/src/index.ts\n\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\n\n// Following the naming conventions from\n// https://tc39.es/source-map/#source-map-format\nconst IGNORE_LIST = 'ignoreList'\n\nconst PLUGIN_NAME = 'devtools-ignore-plugin'\n\ninterface SourceMap {\n  sources: string[]\n  [IGNORE_LIST]: number[]\n}\n\ninterface PluginOptions {\n  shouldIgnorePath?: (path: string) => boolean\n  isSourceMapAsset?: (name: string) => boolean\n}\n\ninterface ValidatedOptions extends PluginOptions {\n  shouldIgnorePath: Required<PluginOptions>['shouldIgnorePath']\n  isSourceMapAsset: Required<PluginOptions>['isSourceMapAsset']\n}\n\nfunction defaultShouldIgnorePath(path: string): boolean {\n  return path.includes('/node_modules/') || path.includes('/webpack/')\n}\n\nfunction defaultIsSourceMapAsset(name: string): boolean {\n  return name.endsWith('.map')\n}\n\n/**\n * This plugin adds a field to source maps that identifies which sources are\n * vendored or runtime-injected (aka third-party) sources. These are consumed by\n * Chrome DevTools to automatically ignore-list sources.\n */\nexport default class DevToolsIgnorePlugin {\n  options: ValidatedOptions\n\n  constructor(options: PluginOptions = {}) {\n    this.options = {\n      shouldIgnorePath: options.shouldIgnorePath ?? defaultShouldIgnorePath,\n      isSourceMapAsset: options.isSourceMapAsset ?? defaultIsSourceMapAsset,\n    }\n  }\n\n  apply(compiler: webpack.Compiler) {\n    const { RawSource } = compiler.webpack.sources\n\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_DEV_TOOLING,\n          additionalAssets: true,\n        },\n        (assets) => {\n          for (const [name, asset] of Object.entries(assets)) {\n            // Instead of using `asset.map()` to fetch the source maps from\n            // SourceMapSource assets, process them directly as a RawSource.\n            // This is because `.map()` is slow and can take several seconds.\n            if (!this.options.isSourceMapAsset(name)) {\n              // Ignore non source map files.\n              continue\n            }\n\n            const mapContent = asset.source().toString()\n            if (!mapContent) {\n              continue\n            }\n\n            const sourcemap = JSON.parse(mapContent) as SourceMap\n\n            const ignoreList = []\n            for (const [index, path] of sourcemap.sources.entries()) {\n              if (this.options.shouldIgnorePath(path)) {\n                ignoreList.push(index)\n              }\n            }\n\n            sourcemap[IGNORE_LIST] = ignoreList\n            compilation.updateAsset(\n              name,\n              new RawSource(JSON.stringify(sourcemap))\n            )\n          }\n        }\n      )\n    })\n  }\n}\n"], "names": ["DevToolsIgnorePlugin", "IGNORE_LIST", "PLUGIN_NAME", "defaultShouldIgnorePath", "path", "includes", "defaultIsSourceMapAsset", "name", "endsWith", "constructor", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isSourceMapAsset", "apply", "compiler", "RawSource", "webpack", "sources", "hooks", "compilation", "tap", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_DEV_TOOLING", "additionalAssets", "assets", "asset", "Object", "entries", "mapContent", "source", "toString", "sourcemap", "JSON", "parse", "ignoreList", "index", "push", "updateAsset", "stringify"], "mappings": "AAAA,kIAAkI;;;;;+BAiClI;;;;CAIC,GACD;;;eAAqBA;;;yBApCG;AAExB,wCAAwC;AACxC,gDAAgD;AAChD,MAAMC,cAAc;AAEpB,MAAMC,cAAc;AAiBpB,SAASC,wBAAwBC,IAAY;IAC3C,OAAOA,KAAKC,QAAQ,CAAC,qBAAqBD,KAAKC,QAAQ,CAAC;AAC1D;AAEA,SAASC,wBAAwBC,IAAY;IAC3C,OAAOA,KAAKC,QAAQ,CAAC;AACvB;AAOe,MAAMR;IAGnBS,YAAYC,UAAyB,CAAC,CAAC,CAAE;QACvC,IAAI,CAACA,OAAO,GAAG;YACbC,kBAAkBD,QAAQC,gBAAgB,IAAIR;YAC9CS,kBAAkBF,QAAQE,gBAAgB,IAAIN;QAChD;IACF;IAEAO,MAAMC,QAA0B,EAAE;QAChC,MAAM,EAAEC,SAAS,EAAE,GAAGD,SAASE,OAAO,CAACC,OAAO;QAE9CH,SAASI,KAAK,CAACC,WAAW,CAACC,GAAG,CAAClB,aAAa,CAACiB;YAC3CA,YAAYD,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEb,MAAML;gBACNoB,OAAON,gBAAO,CAACO,WAAW,CAACC,gCAAgC;gBAC3DC,kBAAkB;YACpB,GACA,CAACC;gBACC,KAAK,MAAM,CAACnB,MAAMoB,MAAM,IAAIC,OAAOC,OAAO,CAACH,QAAS;oBAClD,+DAA+D;oBAC/D,gEAAgE;oBAChE,iEAAiE;oBACjE,IAAI,CAAC,IAAI,CAAChB,OAAO,CAACE,gBAAgB,CAACL,OAAO;wBAExC;oBACF;oBAEA,MAAMuB,aAAaH,MAAMI,MAAM,GAAGC,QAAQ;oBAC1C,IAAI,CAACF,YAAY;wBACf;oBACF;oBAEA,MAAMG,YAAYC,KAAKC,KAAK,CAACL;oBAE7B,MAAMM,aAAa,EAAE;oBACrB,KAAK,MAAM,CAACC,OAAOjC,KAAK,IAAI6B,UAAUhB,OAAO,CAACY,OAAO,GAAI;wBACvD,IAAI,IAAI,CAACnB,OAAO,CAACC,gBAAgB,CAACP,OAAO;4BACvCgC,WAAWE,IAAI,CAACD;wBAClB;oBACF;oBAEAJ,SAAS,CAAChC,YAAY,GAAGmC;oBACzBjB,YAAYoB,WAAW,CACrBhC,MACA,IAAIQ,UAAUmB,KAAKM,SAAS,CAACP;gBAEjC;YACF;QAEJ;IACF;AACF"}