{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/Error.ts"], "sourcesContent": ["/**\n * **PostCSS Syntax Error**\n *\n * Loader wrapper for postcss syntax errors\n *\n * @class SyntaxError\n * @extends Error\n *\n * @param {Object} err CssSyntaxError\n */\nexport default class PostCSSSyntaxError extends Error {\n  stack: any\n  constructor(error: any) {\n    super(error)\n\n    const { line, column, reason, plugin, file } = error\n\n    this.name = 'SyntaxError'\n\n    this.message = `${this.name}\\n\\n`\n\n    if (typeof line !== 'undefined') {\n      this.message += `(${line}:${column}) `\n    }\n\n    this.message += plugin ? `${plugin}: ` : ''\n    this.message += file ? `${file} ` : '<css input> '\n    this.message += reason\n\n    const code = error.showSourceCode()\n\n    if (code) {\n      this.message += `\\n\\n${code}\\n`\n    }\n\n    this.stack = false\n  }\n}\n"], "names": ["PostCSSSyntaxError", "Error", "constructor", "error", "line", "column", "reason", "plugin", "file", "name", "message", "code", "showSourceCode", "stack"], "mappings": "AAAA;;;;;;;;;CASC;;;;+BACD;;;eAAqBA;;;AAAN,MAAMA,2BAA2BC;IAE9CC,YAAYC,KAAU,CAAE;QACtB,KAAK,CAACA;QAEN,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE,GAAGL;QAE/C,IAAI,CAACM,IAAI,GAAG;QAEZ,IAAI,CAACC,OAAO,GAAG,GAAG,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;QAEjC,IAAI,OAAOL,SAAS,aAAa;YAC/B,IAAI,CAACM,OAAO,IAAI,CAAC,CAAC,EAAEN,KAAK,CAAC,EAAEC,OAAO,EAAE,CAAC;QACxC;QAEA,IAAI,CAACK,OAAO,IAAIH,SAAS,GAAGA,OAAO,EAAE,CAAC,GAAG;QACzC,IAAI,CAACG,OAAO,IAAIF,OAAO,GAAGA,KAAK,CAAC,CAAC,GAAG;QACpC,IAAI,CAACE,OAAO,IAAIJ;QAEhB,MAAMK,OAAOR,MAAMS,cAAc;QAEjC,IAAID,MAAM;YACR,IAAI,CAACD,OAAO,IAAI,CAAC,IAAI,EAAEC,KAAK,EAAE,CAAC;QACjC;QAEA,IAAI,CAACE,KAAK,GAAG;IACf;AACF"}