---
title: "Getting Started with .NET MAUI: A Complete Guide"
excerpt: "Learn how to build cross-platform mobile applications using .NET MAUI with this comprehensive guide covering setup, UI design, and deployment."
date: "2024-01-15"
category: "dotnet-maui"
categoryLabel: ".NET MAUI"
readTime: "8 min read"
tags: ["MAUI", "Mobile", "Cross-platform", "Tutorial"]
author: "<PERSON><PERSON><PERSON>"
---

# Getting Started with .NET MAUI: A Complete Guide

.NET Multi-platform App UI (.NET MAUI) is the evolution of Xamarin.Forms, extending it from mobile to desktop scenarios, with UI controls rebuilt from the ground up for performance and extensibility.

## What is .NET MAUI?

.NET MAUI is a cross-platform framework for creating native mobile and desktop apps with C# and XAML. Using .NET MAUI, you can develop apps that can run on Android, iOS, macOS, and Windows from a single shared code-base.

## Prerequisites

Before we start, make sure you have the following installed:

- Visual Studio 2022 17.3 or later with the .NET MAUI workload
- .NET 6 or later
- Android SDK (for Android development)
- Xcode (for iOS/macOS development, Mac only)

## Creating Your First .NET MAUI Project

Let's create a simple "Hello World" application:

```bash
dotnet new maui -n MyFirstMauiApp
cd MyFirstMauiApp
```

This creates a new .NET MAUI project with the following structure:

```
MyFirstMauiApp/
├── Platforms/
│   ├── Android/
│   ├── iOS/
│   ├── MacCatalyst/
│   └── Windows/
├── Resources/
├── MainPage.xaml
├── MainPage.xaml.cs
├── App.xaml
├── App.xaml.cs
├── AppShell.xaml
├── AppShell.xaml.cs
└── MauiProgram.cs
```

## Understanding the Project Structure

### MauiProgram.cs
This is the entry point of your application where you configure services and dependencies:

```csharp
public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
            });

        return builder.Build();
    }
}
```

### App.xaml and App.xaml.cs
These files define the application-level resources and handle application lifecycle events.

### AppShell.xaml
This defines the visual hierarchy of your application and handles navigation.

## Building Your First Page

Let's modify the MainPage.xaml to create a simple counter app:

```xml
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="MyFirstMauiApp.MainPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <ScrollView>
        <VerticalStackLayout Spacing="25" Padding="30,0" VerticalOptions="Center">
            
            <Image Source="dotnet_bot.png"
                   SemanticProperties.Description="Cute dot net bot waving hi to you!"
                   HeightRequest="200"
                   HorizontalOptions="Center" />
            
            <Label x:Name="CounterLabel"
                   Text="Current count: 0"
                   SemanticProperties.HeadingLevel="Level1"
                   FontSize="18"
                   HorizontalOptions="Center" />
            
            <Button x:Name="CounterBtn"
                    Text="Click me"
                    SemanticProperties.Hint="Counts the number of times you click"
                    Clicked="OnCounterClicked"
                    HorizontalOptions="Center" />

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
```

And the corresponding code-behind in MainPage.xaml.cs:

```csharp
namespace MyFirstMauiApp;

public partial class MainPage : ContentPage
{
    int count = 0;

    public MainPage()
    {
        InitializeComponent();
    }

    private void OnCounterClicked(object sender, EventArgs e)
    {
        count++;

        if (count == 1)
            CounterBtn.Text = $"Clicked {count} time";
        else
            CounterBtn.Text = $"Clicked {count} times";

        CounterLabel.Text = $"Current count: {count}";

        SemanticScreenReader.Announce(CounterLabel.Text);
    }
}
```

## Running Your Application

To run your application on different platforms:

### Android
```bash
dotnet build -t:Run -f net6.0-android
```

### iOS (Mac only)
```bash
dotnet build -t:Run -f net6.0-ios
```

### Windows
```bash
dotnet build -t:Run -f net6.0-windows10.0.19041.0
```

## Key Concepts to Remember

1. **Single Project**: .NET MAUI uses a single project structure that can target multiple platforms
2. **Hot Reload**: You can make changes to your XAML and see them immediately without rebuilding
3. **Platform-Specific Code**: Use the Platforms folder for platform-specific implementations
4. **Dependency Injection**: Built-in support for dependency injection through MauiProgram.cs

## Next Steps

Now that you have a basic .NET MAUI application running, you can explore:

- MVVM pattern implementation
- Data binding
- Navigation between pages
- Platform-specific features
- Custom controls and renderers

## Conclusion

.NET MAUI provides a powerful framework for building cross-platform applications with a single codebase. This guide covered the basics to get you started, but there's much more to explore as you build more complex applications.

In the next post, we'll dive deeper into MVVM patterns and data binding in .NET MAUI applications.
