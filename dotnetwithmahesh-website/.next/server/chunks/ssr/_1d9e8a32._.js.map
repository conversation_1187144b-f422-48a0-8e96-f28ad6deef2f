{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/DotNetwithMahesh/dotnetwithmahesh-website/src/app/about/page.tsx"], "sourcesContent": ["import { <PERSON>ada<PERSON> } from 'next';\nimport <PERSON> from 'next/link';\nimport { ArrowRight, Calendar, MapPin, Mail, Linkedin, Github } from 'lucide-react';\n\nexport const metadata: Metadata = {\n  title: 'About <PERSON><PERSON><PERSON>have',\n  description: 'Learn about <PERSON><PERSON><PERSON>\\'s professional journey from X<PERSON>rin to .NET MAUI to cloud development, and his mission to share daily insights with the developer community.',\n};\n\nconst timelineEvents = [\n  {\n    year: '2016',\n    title: 'Started with Xamarin',\n    description: 'Began my mobile development journey with Xamarin.Forms, building cross-platform applications for various clients.'\n  },\n  {\n    year: '2018',\n    title: 'Azure Cloud Adoption',\n    description: 'Expanded into cloud development with Microsoft Azure, focusing on serverless architectures and cloud-native applications.'\n  },\n  {\n    year: '2020',\n    title: 'AWS Expertise',\n    description: 'Diversified cloud skills by mastering Amazon Web Services, implementing scalable solutions and DevOps practices.'\n  },\n  {\n    year: '2022',\n    title: '.NET MAUI Migration',\n    description: 'Led multiple Xamarin.Forms to .NET MAUI migration projects, helping teams modernize their mobile applications.'\n  },\n  {\n    year: '2024',\n    title: 'Daily Blogging Journey',\n    description: 'Started this blog to share daily insights and help fellow developers navigate the evolving .NET ecosystem.'\n  }\n];\n\nconst skills = [\n  { category: 'Mobile Development', items: ['.NET MAUI', 'Xamarin.Forms', 'Xamarin.Native', 'MVVM', 'Prism'] },\n  { category: 'Cloud Platforms', items: ['Microsoft Azure', 'Amazon AWS', 'Azure Functions', 'AWS Lambda', 'App Service'] },\n  { category: 'Programming', items: ['C#', '.NET 8', 'ASP.NET Core', 'Entity Framework', 'SignalR'] },\n  { category: 'DevOps & Tools', items: ['Azure DevOps', 'GitHub Actions', 'Docker', 'Terraform', 'PowerShell'] }\n];\n\nexport default function About() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <div>\n              <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n                Hi, I'm <span className=\"text-blue-600\">Mahesh Gadhave</span>\n              </h1>\n              <p className=\"text-xl text-gray-600 mb-6\">\n                A passionate .NET developer with 8+ years of experience building mobile and cloud applications. \n                I specialize in .NET MAUI, Xamarin, Azure, and AWS technologies.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link href=\"/contact\" className=\"btn-primary inline-flex items-center\">\n                  Get In Touch\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Link>\n                <a \n                  href=\"/resume.pdf\" \n                  target=\"_blank\" \n                  rel=\"noopener noreferrer\"\n                  className=\"btn-secondary inline-flex items-center\"\n                >\n                  Download Resume\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </a>\n              </div>\n            </div>\n            <div className=\"flex justify-center\">\n              <div className=\"w-80 h-80 bg-gray-200 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-500\">Professional Photo</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Professional Journey */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">My Professional Journey</h2>\n            <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n              From building my first Xamarin app to architecting cloud-native solutions, \n              here's how my career has evolved over the years.\n            </p>\n          </div>\n          \n          <div className=\"relative\">\n            <div className=\"absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gray-300\"></div>\n            <div className=\"space-y-12\">\n              {timelineEvents.map((event, index) => (\n                <div key={event.year} className={`relative flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>\n                  <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>\n                    <div className=\"bg-white p-6 rounded-lg shadow-md\">\n                      <div className=\"text-blue-600 font-bold text-lg mb-2\">{event.year}</div>\n                      <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{event.title}</h3>\n                      <p className=\"text-gray-600\">{event.description}</p>\n                    </div>\n                  </div>\n                  <div className=\"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Mission Statement */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">My Mission</h2>\n          <p className=\"text-xl text-gray-600 mb-8\">\n            \"To share daily insights and practical knowledge that helps fellow developers build better applications \n            and advance their careers in the ever-evolving world of .NET and cloud development.\"\n          </p>\n          <div className=\"bg-white p-8 rounded-lg shadow-md\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Why I Blog Daily</h3>\n            <p className=\"text-lg text-gray-600 mb-4\">\n              I believe that consistent learning and sharing knowledge is the key to growth in our industry. \n              By committing to daily blogging, I not only reinforce my own learning but also create a valuable \n              resource for the developer community.\n            </p>\n            <p className=\"text-lg text-gray-600\">\n              Each post is crafted from real-world experience, whether it's solving a complex migration challenge, \n              optimizing cloud costs, or discovering a new feature in .NET MAUI.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Skills & Expertise */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Skills & Expertise</h2>\n            <p className=\"text-lg text-gray-600\">\n              Technologies and platforms I work with professionally and write about regularly\n            </p>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {skills.map((skillGroup) => (\n              <div key={skillGroup.category} className=\"bg-gray-50 p-6 rounded-lg\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">{skillGroup.category}</h3>\n                <ul className=\"space-y-2\">\n                  {skillGroup.items.map((skill) => (\n                    <li key={skill} className=\"text-gray-600 flex items-center\">\n                      <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                      {skill}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Information */}\n      <section className=\"py-16 bg-blue-600\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-6\">Let's Connect</h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            I'm always interested in connecting with fellow developers, discussing new technologies, \n            or exploring collaboration opportunities.\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <a \n              href=\"mailto:<EMAIL>\"\n              className=\"bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-lg hover:bg-opacity-20 transition-all duration-200\"\n            >\n              <Mail className=\"w-8 h-8 text-white mx-auto mb-3\" />\n              <h3 className=\"text-white font-semibold mb-2\">Email</h3>\n              <p className=\"text-blue-100 text-sm\"><EMAIL></p>\n            </a>\n            <a\n              href=\"https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-lg hover:bg-opacity-20 transition-all duration-200\"\n            >\n              <Linkedin className=\"w-8 h-8 text-white mx-auto mb-3\" />\n              <h3 className=\"text-white font-semibold mb-2\">LinkedIn</h3>\n              <p className=\"text-blue-100 text-sm\">Professional Network</p>\n            </a>\n            <a \n              href=\"https://github.com/maheshgadhave\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-lg hover:bg-opacity-20 transition-all duration-200\"\n            >\n              <Github className=\"w-8 h-8 text-white mx-auto mb-3\" />\n              <h3 className=\"text-white font-semibold mb-2\">GitHub</h3>\n              <p className=\"text-blue-100 text-sm\">Code & Projects</p>\n            </a>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AAAA;AAAA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEA,MAAM,iBAAiB;IACrB;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,SAAS;IACb;QAAE,UAAU;QAAsB,OAAO;YAAC;YAAa;YAAiB;YAAkB;YAAQ;SAAQ;IAAC;IAC3G;QAAE,UAAU;QAAmB,OAAO;YAAC;YAAmB;YAAc;YAAmB;YAAc;SAAc;IAAC;IACxH;QAAE,UAAU;QAAe,OAAO;YAAC;YAAM;YAAU;YAAgB;YAAoB;SAAU;IAAC;IAClG;QAAE,UAAU;QAAkB,OAAO;YAAC;YAAgB;YAAkB;YAAU;YAAa;SAAa;IAAC;CAC9G;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAAoD;0DACxD,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAI1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;;oDAAuC;kEAErE,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAI5B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;4CAAqB,WAAW,CAAC,2BAA2B,EAAE,QAAQ,MAAM,IAAI,kBAAkB,eAAe;;8DAChH,8OAAC;oDAAI,WAAW,CAAC,OAAO,EAAE,QAAQ,MAAM,IAAI,oBAAoB,kBAAkB;8DAChF,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAwC,MAAM,IAAI;;;;;;0EACjE,8OAAC;gEAAG,WAAU;0EAAwC,MAAM,KAAK;;;;;;0EACjE,8OAAC;gEAAE,WAAU;0EAAiB,MAAM,WAAW;;;;;;;;;;;;;;;;;8DAGnD,8OAAC;oDAAI,WAAU;;;;;;;2CARP,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiB9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAK1C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,2BACX,8OAAC;oCAA8B,WAAU;;sDACvC,8OAAC;4CAAG,WAAU;sDAAwC,WAAW,QAAQ;;;;;;sDACzE,8OAAC;4CAAG,WAAU;sDACX,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,sBACrB,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;;;;;;wDACd;;mDAFM;;;;;;;;;;;mCAJL,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;0BAiBrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/DotNetwithMahesh/dotnetwithmahesh-website/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/DotNetwithMahesh/dotnetwithmahesh-website/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/DotNetwithMahesh/dotnetwithmahesh-website/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}