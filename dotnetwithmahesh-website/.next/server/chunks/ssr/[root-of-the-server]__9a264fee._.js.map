{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/DotNetwithMahesh/dotnetwithmahesh-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { Menu, X } from 'lucide-react';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'About', href: '/about' },\n    { name: 'Blog', href: '/blog' },\n    { name: 'Projects', href: '/projects' },\n    { name: 'Contact', href: '/contact' },\n    { name: 'Newsletter', href: '/newsletter' },\n  ];\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"bg-blue-600 text-white px-3 py-2 rounded-lg font-bold text-lg\">\n                DM\n              </div>\n              <span className=\"ml-3 text-xl font-bold text-gray-900 hidden sm:block\">\n                DotNet with Mahesh\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600\"\n            >\n              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAc,MAAM;QAAc;KAC3C;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDAAgE;;;;;;kDAG/E,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;;;;;;sCAO3E,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gBAMjD,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,IAAI;+BALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;uCAEe", "debugId": null}}]}