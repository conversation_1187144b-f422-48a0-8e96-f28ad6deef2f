{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/DotNetwithMahesh/dotnetwithmahesh-website/src/app/blog/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport Link from 'next/link';\nimport { notFound } from 'next/navigation';\nimport { Calendar, Clock, Tag, ArrowLeft, ArrowRight, Share2, Linkedin, Twitter, Facebook } from 'lucide-react';\n\n// Mock blog post data - in a real app, this would come from your CMS or API\nconst blogPosts = {\n  'getting-started-with-dotnet-maui': {\n    id: 1,\n    title: \"Getting Started with .NET MAUI: A Complete Guide\",\n    excerpt: \"Learn how to build cross-platform mobile applications using .NET MAUI with this comprehensive guide covering setup, UI design, and deployment.\",\n    content: `\n# Getting Started with .NET MAUI: A Complete Guide\n\n.NET Multi-platform App UI (.NET MAUI) is the evolution of Xamarin.Forms, extending it from mobile to desktop scenarios, with UI controls rebuilt from the ground up for performance and extensibility.\n\n## What is .NET MAUI?\n\n.NET MAUI is a cross-platform framework for creating native mobile and desktop apps with C# and XAML. Using .NET MAUI, you can develop apps that can run on Android, iOS, macOS, and Windows from a single shared code-base.\n\n## Prerequisites\n\nBefore we start, make sure you have the following installed:\n\n- Visual Studio 2022 17.3 or later with the .NET MAUI workload\n- .NET 6 or later\n- Android SDK (for Android development)\n- Xcode (for iOS/macOS development, Mac only)\n\n## Creating Your First .NET MAUI Project\n\nLet's create a simple \"Hello World\" application:\n\n\\`\\`\\`bash\ndotnet new maui -n MyFirstMauiApp\ncd MyFirstMauiApp\n\\`\\`\\`\n\nThis creates a new .NET MAUI project with the following structure:\n\n\\`\\`\\`\nMyFirstMauiApp/\n├── Platforms/\n│   ├── Android/\n│   ├── iOS/\n│   ├── MacCatalyst/\n│   └── Windows/\n├── Resources/\n├── MainPage.xaml\n├── MainPage.xaml.cs\n├── App.xaml\n├── App.xaml.cs\n├── AppShell.xaml\n├── AppShell.xaml.cs\n└── MauiProgram.cs\n\\`\\`\\`\n\n## Understanding the Project Structure\n\n### MauiProgram.cs\nThis is the entry point of your application where you configure services and dependencies:\n\n\\`\\`\\`csharp\npublic static class MauiProgram\n{\n    public static MauiApp CreateMauiApp()\n    {\n        var builder = MauiApp.CreateBuilder();\n        builder\n            .UseMauiApp<App>()\n            .ConfigureFonts(fonts =>\n            {\n                fonts.AddFont(\"OpenSans-Regular.ttf\", \"OpenSansRegular\");\n            });\n\n        return builder.Build();\n    }\n}\n\\`\\`\\`\n\n### App.xaml and App.xaml.cs\nThese files define the application-level resources and handle application lifecycle events.\n\n### AppShell.xaml\nThis defines the visual hierarchy of your application and handles navigation.\n\n## Building Your First Page\n\nLet's modify the MainPage.xaml to create a simple counter app:\n\n\\`\\`\\`xml\n<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n<ContentPage x:Class=\"MyFirstMauiApp.MainPage\"\n             xmlns=\"http://schemas.microsoft.com/dotnet/2021/maui\"\n             xmlns:x=\"http://schemas.microsoft.com/winfx/2009/xaml\">\n\n    <ScrollView>\n        <VerticalStackLayout Spacing=\"25\" Padding=\"30,0\" VerticalOptions=\"Center\">\n            \n            <Image Source=\"dotnet_bot.png\"\n                   SemanticProperties.Description=\"Cute dot net bot waving hi to you!\"\n                   HeightRequest=\"200\"\n                   HorizontalOptions=\"Center\" />\n            \n            <Label x:Name=\"CounterLabel\"\n                   Text=\"Current count: 0\"\n                   SemanticProperties.HeadingLevel=\"Level1\"\n                   FontSize=\"18\"\n                   HorizontalOptions=\"Center\" />\n            \n            <Button x:Name=\"CounterBtn\"\n                    Text=\"Click me\"\n                    SemanticProperties.Hint=\"Counts the number of times you click\"\n                    Clicked=\"OnCounterClicked\"\n                    HorizontalOptions=\"Center\" />\n\n        </VerticalStackLayout>\n    </ScrollView>\n\n</ContentPage>\n\\`\\`\\`\n\nAnd the corresponding code-behind in MainPage.xaml.cs:\n\n\\`\\`\\`csharp\nnamespace MyFirstMauiApp;\n\npublic partial class MainPage : ContentPage\n{\n    int count = 0;\n\n    public MainPage()\n    {\n        InitializeComponent();\n    }\n\n    private void OnCounterClicked(object sender, EventArgs e)\n    {\n        count++;\n\n        if (count == 1)\n            CounterBtn.Text = $\"Clicked {count} time\";\n        else\n            CounterBtn.Text = $\"Clicked {count} times\";\n\n        CounterLabel.Text = $\"Current count: {count}\";\n\n        SemanticScreenReader.Announce(CounterLabel.Text);\n    }\n}\n\\`\\`\\`\n\n## Running Your Application\n\nTo run your application on different platforms:\n\n### Android\n\\`\\`\\`bash\ndotnet build -t:Run -f net6.0-android\n\\`\\`\\`\n\n### iOS (Mac only)\n\\`\\`\\`bash\ndotnet build -t:Run -f net6.0-ios\n\\`\\`\\`\n\n### Windows\n\\`\\`\\`bash\ndotnet build -t:Run -f net6.0-windows10.0.19041.0\n\\`\\`\\`\n\n## Key Concepts to Remember\n\n1. **Single Project**: .NET MAUI uses a single project structure that can target multiple platforms\n2. **Hot Reload**: You can make changes to your XAML and see them immediately without rebuilding\n3. **Platform-Specific Code**: Use the Platforms folder for platform-specific implementations\n4. **Dependency Injection**: Built-in support for dependency injection through MauiProgram.cs\n\n## Next Steps\n\nNow that you have a basic .NET MAUI application running, you can explore:\n\n- MVVM pattern implementation\n- Data binding\n- Navigation between pages\n- Platform-specific features\n- Custom controls and renderers\n\n## Conclusion\n\n.NET MAUI provides a powerful framework for building cross-platform applications with a single codebase. This guide covered the basics to get you started, but there's much more to explore as you build more complex applications.\n\nIn the next post, we'll dive deeper into MVVM patterns and data binding in .NET MAUI applications.\n    `,\n    date: \"2024-01-15\",\n    category: \"dotnet-maui\",\n    categoryLabel: \".NET MAUI\",\n    readTime: \"8 min read\",\n    tags: [\"MAUI\", \"Mobile\", \"Cross-platform\", \"Tutorial\"],\n    author: \"Mahesh Gadhave\"\n  }\n  // Add more blog posts here...\n};\n\n// Mock related posts\nconst relatedPosts = [\n  {\n    id: 2,\n    title: \"Migrating from Xamarin.Forms to .NET MAUI\",\n    slug: \"migrating-xamarin-forms-to-dotnet-maui\",\n    category: \"Migration\"\n  },\n  {\n    id: 5,\n    title: \"State Management in .NET MAUI Applications\",\n    slug: \"state-management-dotnet-maui\",\n    category: \".NET MAUI\"\n  },\n  {\n    id: 7,\n    title: \"Implementing Authentication in .NET MAUI\",\n    slug: \"authentication-dotnet-maui\",\n    category: \".NET MAUI\"\n  }\n];\n\ninterface BlogPostPageProps {\n  params: {\n    slug: string;\n  };\n}\n\nexport async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {\n  const post = blogPosts[params.slug as keyof typeof blogPosts];\n  \n  if (!post) {\n    return {\n      title: 'Post Not Found',\n    };\n  }\n\n  return {\n    title: post.title,\n    description: post.excerpt,\n    openGraph: {\n      title: post.title,\n      description: post.excerpt,\n      type: 'article',\n      publishedTime: post.date,\n      authors: [post.author],\n    },\n    twitter: {\n      card: 'summary_large_image',\n      title: post.title,\n      description: post.excerpt,\n    },\n  };\n}\n\nexport default function BlogPost({ params }: BlogPostPageProps) {\n  const post = blogPosts[params.slug as keyof typeof blogPosts];\n\n  if (!post) {\n    notFound();\n  }\n\n  const shareUrl = `https://dotnetwithmahesh.dev/blog/${params.slug}`;\n  const shareText = `Check out this article: ${post.title}`;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Article Header */}\n      <article className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n          {/* Featured Image Placeholder */}\n          <div className=\"h-64 md:h-80 bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center\">\n            <div className=\"text-center text-white\">\n              <h1 className=\"text-2xl md:text-4xl font-bold mb-4 px-6\">{post.title}</h1>\n              <div className=\"flex items-center justify-center gap-6 text-blue-100\">\n                <div className=\"flex items-center\">\n                  <Calendar className=\"w-4 h-4 mr-2\" />\n                  {new Date(post.date).toLocaleDateString('en-US', { \n                    year: 'numeric', \n                    month: 'long', \n                    day: 'numeric' \n                  })}\n                </div>\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-4 h-4 mr-2\" />\n                  {post.readTime}\n                </div>\n                <div className=\"flex items-center\">\n                  <Tag className=\"w-4 h-4 mr-2\" />\n                  {post.categoryLabel}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Article Content */}\n          <div className=\"p-8\">\n            {/* Tags */}\n            <div className=\"flex flex-wrap gap-2 mb-6\">\n              {post.tags.map((tag) => (\n                <span key={tag} className=\"bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full\">\n                  {tag}\n                </span>\n              ))}\n            </div>\n\n            {/* Content */}\n            <div className=\"prose prose-lg max-w-none\">\n              <div dangerouslySetInnerHTML={{ __html: post.content.replace(/\\n/g, '<br />') }} />\n            </div>\n\n            {/* Share Buttons */}\n            <div className=\"mt-12 pt-8 border-t border-gray-200\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <Share2 className=\"w-5 h-5 mr-2\" />\n                Share this article\n              </h3>\n              <div className=\"flex gap-4\">\n                <a\n                  href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  <Linkedin className=\"w-4 h-4\" />\n                  LinkedIn\n                </a>\n                <a\n                  href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center gap-2 bg-sky-500 text-white px-4 py-2 rounded-lg hover:bg-sky-600 transition-colors duration-200\"\n                >\n                  <Twitter className=\"w-4 h-4\" />\n                  Twitter\n                </a>\n                <a\n                  href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center gap-2 bg-blue-800 text-white px-4 py-2 rounded-lg hover:bg-blue-900 transition-colors duration-200\"\n                >\n                  <Facebook className=\"w-4 h-4\" />\n                  Facebook\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex justify-between items-center mt-8\">\n          <Link href=\"/blog\" className=\"flex items-center text-blue-600 hover:text-blue-800 font-medium\">\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            Back to Blog\n          </Link>\n          <div className=\"flex gap-4\">\n            <button className=\"flex items-center text-gray-500 hover:text-gray-700\">\n              <ArrowLeft className=\"w-4 h-4 mr-1\" />\n              Previous\n            </button>\n            <button className=\"flex items-center text-gray-500 hover:text-gray-700\">\n              Next\n              <ArrowRight className=\"w-4 h-4 ml-1\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Related Posts */}\n        <div className=\"mt-16\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">Related Posts</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {relatedPosts.map((relatedPost) => (\n              <Link key={relatedPost.id} href={`/blog/${relatedPost.slug}`}>\n                <div className=\"bg-white rounded-lg shadow-md p-6 card-hover\">\n                  <span className=\"text-sm font-medium text-blue-600 mb-2 block\">{relatedPost.category}</span>\n                  <h3 className=\"font-semibold text-gray-900 hover:text-blue-600 transition-colors duration-200\">\n                    {relatedPost.title}\n                  </h3>\n                </div>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </article>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAEA,4EAA4E;AAC5E,MAAM,YAAY;IAChB,oCAAoC;QAClC,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsLV,CAAC;QACD,MAAM;QACN,UAAU;QACV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAQ;YAAU;YAAkB;SAAW;QACtD,QAAQ;IACV;AAEF;AAEA,qBAAqB;AACrB,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;IACZ;CACD;AAQM,eAAe,iBAAiB,EAAE,MAAM,EAAqB;IAClE,MAAM,OAAO,SAAS,CAAC,OAAO,IAAI,CAA2B;IAE7D,IAAI,CAAC,MAAM;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;QACL,OAAO,KAAK,KAAK;QACjB,aAAa,KAAK,OAAO;QACzB,WAAW;YACT,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;YACzB,MAAM;YACN,eAAe,KAAK,IAAI;YACxB,SAAS;gBAAC,KAAK,MAAM;aAAC;QACxB;QACA,SAAS;YACP,MAAM;YACN,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;QAC3B;IACF;AACF;AAEe,SAAS,SAAS,EAAE,MAAM,EAAqB;IAC5D,MAAM,OAAO,SAAS,CAAC,OAAO,IAAI,CAA2B;IAE7D,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,WAAW,CAAC,kCAAkC,EAAE,OAAO,IAAI,EAAE;IACnE,MAAM,YAAY,CAAC,wBAAwB,EAAE,KAAK,KAAK,EAAE;IAEzD,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAQ,WAAU;;8BACjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C,KAAK,KAAK;;;;;;kDACpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;wDAC/C,MAAM;wDACN,OAAO;wDACP,KAAK;oDACP;;;;;;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,KAAK,QAAQ;;;;;;;0DAEhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAO3B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,8OAAC;4CAAe,WAAU;sDACvB;2CADQ;;;;;;;;;;8CAOf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,yBAAyB;4CAAE,QAAQ,KAAK,OAAO,CAAC,OAAO,CAAC,OAAO;wCAAU;;;;;;;;;;;8CAIhF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAM,CAAC,oDAAoD,EAAE,mBAAmB,WAAW;oDAC3F,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGlC,8OAAC;oDACC,MAAM,CAAC,qCAAqC,EAAE,mBAAmB,UAAU,MAAM,EAAE,mBAAmB,YAAY;oDAClH,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGjC,8OAAC;oDACC,MAAM,CAAC,6CAA6C,EAAE,mBAAmB,WAAW;oDACpF,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAQ,WAAU;;8CAC3B,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,8OAAC;oCAAO,WAAU;;wCAAsD;sDAEtE,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAM5B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC,4JAAA,CAAA,UAAI;oCAAsB,MAAM,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE;8CAC1D,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgD,YAAY,QAAQ;;;;;;0DACpF,8OAAC;gDAAG,WAAU;0DACX,YAAY,KAAK;;;;;;;;;;;;mCAJb,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcvC", "debugId": null}}]}