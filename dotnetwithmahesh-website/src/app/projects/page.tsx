'use client';

import { useState } from 'react';
import { Metadata } from 'next';
import { ExternalLink, Github, Filter, Smartphone, Cloud, Code, Database, Globe } from 'lucide-react';

// Mock projects data
const projects = [
  {
    id: 1,
    title: "Milk Delivery App",
    description: "A comprehensive milk delivery application built with .NET MAUI, featuring customer management, order tracking, and offline capabilities with SQLite database.",
    technologies: [".NET MAUI", "SQLite", "MVVM", "C#"],
    category: "mobile",
    image: "/api/placeholder/400/250",
    githubUrl: "https://github.com/maheshgadhave/milk-delivery-app",
    liveUrl: null,
    features: [
      "Cross-platform mobile app (iOS & Android)",
      "Offline-first architecture with SQLite",
      "Real-time order tracking",
      "Customer management system",
      "Payment integration",
      "Push notifications"
    ],
    status: "Completed"
  },
  {
    id: 2,
    title: "Task Manager with AWS Cognito",
    description: "A cloud-native task management application using AWS services including Cognito for authentication, Lambda for serverless backend, and DynamoDB for data storage.",
    technologies: ["AWS Lambda", "AWS Cognito", "DynamoDB", ".NET 8", "React"],
    category: "cloud",
    image: "/api/placeholder/400/250",
    githubUrl: "https://github.com/maheshgadhave/aws-task-manager",
    liveUrl: "https://taskmanager.dotnetwithmahesh.dev",
    features: [
      "Serverless architecture with AWS Lambda",
      "User authentication with AWS Cognito",
      "Real-time updates with WebSockets",
      "Scalable NoSQL database with DynamoDB",
      "CI/CD pipeline with GitHub Actions",
      "Cost-optimized cloud infrastructure"
    ],
    status: "Completed"
  },
  {
    id: 3,
    title: "Real-time Chat Application",
    description: "A modern chat application built with .NET MAUI for mobile clients and ASP.NET Core with SignalR for real-time communication, integrated with Firebase for push notifications.",
    technologies: [".NET MAUI", "SignalR", "Firebase", "ASP.NET Core", "Azure"],
    category: "mobile",
    image: "/api/placeholder/400/250",
    githubUrl: "https://github.com/maheshgadhave/realtime-chat-app",
    liveUrl: null,
    features: [
      "Real-time messaging with SignalR",
      "Cross-platform mobile app",
      "Push notifications with Firebase",
      "File and image sharing",
      "Group chat functionality",
      "Message encryption"
    ],
    status: "In Progress"
  },
  {
    id: 4,
    title: "E-commerce Analytics Dashboard",
    description: "A comprehensive analytics dashboard for e-commerce businesses built with Blazor Server, featuring real-time data visualization and reporting capabilities.",
    technologies: ["Blazor Server", "Azure SQL", "Chart.js", "Entity Framework", "Azure"],
    category: "web",
    image: "/api/placeholder/400/250",
    githubUrl: "https://github.com/maheshgadhave/ecommerce-analytics",
    liveUrl: "https://analytics.dotnetwithmahesh.dev",
    features: [
      "Real-time data visualization",
      "Interactive charts and graphs",
      "Custom report generation",
      "Role-based access control",
      "Export functionality (PDF, Excel)",
      "Mobile-responsive design"
    ],
    status: "Completed"
  },
  {
    id: 5,
    title: "DevOps Pipeline Automation",
    description: "A comprehensive DevOps solution for .NET applications featuring automated CI/CD pipelines, infrastructure as code, and monitoring setup.",
    technologies: ["Azure DevOps", "Terraform", "Docker", "Kubernetes", "PowerShell"],
    category: "devops",
    image: "/api/placeholder/400/250",
    githubUrl: "https://github.com/maheshgadhave/devops-automation",
    liveUrl: null,
    features: [
      "Automated CI/CD pipelines",
      "Infrastructure as Code with Terraform",
      "Container orchestration with Kubernetes",
      "Automated testing and deployment",
      "Monitoring and alerting setup",
      "Security scanning integration"
    ],
    status: "Completed"
  },
  {
    id: 6,
    title: "Microservices Architecture Demo",
    description: "A demonstration of microservices architecture using .NET 8, featuring API Gateway, service discovery, and distributed tracing.",
    technologies: [".NET 8", "Docker", "RabbitMQ", "Redis", "Ocelot", "Consul"],
    category: "cloud",
    image: "/api/placeholder/400/250",
    githubUrl: "https://github.com/maheshgadhave/microservices-demo",
    liveUrl: null,
    features: [
      "API Gateway with Ocelot",
      "Service discovery with Consul",
      "Message queuing with RabbitMQ",
      "Distributed caching with Redis",
      "Health checks and monitoring",
      "Docker containerization"
    ],
    status: "In Progress"
  }
];

const categories = [
  { id: 'all', label: 'All Projects', icon: Globe },
  { id: 'mobile', label: 'Mobile Apps', icon: Smartphone },
  { id: 'web', label: 'Web Applications', icon: Code },
  { id: 'cloud', label: 'Cloud Solutions', icon: Cloud },
  { id: 'devops', label: 'DevOps', icon: Database },
];

export default function ProjectsPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredProjects = projects.filter(project => 
    selectedCategory === 'all' || project.category === selectedCategory
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Projects Showcase
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore my portfolio of real-world applications and demonstrations showcasing 
              .NET MAUI, cloud technologies, and modern development practices.
            </p>
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center">
            <div className="flex flex-wrap gap-4">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                      selectedCategory === category.id
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {category.label}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <p className="text-gray-600">
              Showing {filteredProjects.length} of {projects.length} projects
              {selectedCategory !== 'all' && ` in ${categories.find(c => c.id === selectedCategory)?.label}`}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project) => (
              <div key={project.id} className="bg-white rounded-lg shadow-lg overflow-hidden card-hover">
                {/* Project Image */}
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Project Screenshot</span>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  {/* Status Badge */}
                  <div className="flex items-center justify-between mb-3">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      project.status === 'Completed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {project.status}
                    </span>
                  </div>

                  {/* Title and Description */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{project.title}</h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">{project.description}</p>

                  {/* Technologies */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Technologies:</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech) => (
                        <span key={tech} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Features:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {project.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                          {feature}
                        </li>
                      ))}
                      {project.features.length > 3 && (
                        <li className="text-blue-600 text-sm">
                          +{project.features.length - 3} more features
                        </li>
                      )}
                    </ul>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    {project.githubUrl && (
                      <a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors duration-200 text-sm font-medium"
                      >
                        <Github className="w-4 h-4" />
                        Code
                      </a>
                    )}
                    {project.liveUrl && (
                      <a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
                      >
                        <ExternalLink className="w-4 h-4" />
                        Live Demo
                      </a>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No projects found in this category.</p>
              <button
                onClick={() => setSelectedCategory('all')}
                className="mt-4 text-blue-600 hover:text-blue-800 font-medium"
              >
                View all projects
              </button>
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">Interested in Collaboration?</h2>
          <p className="text-xl text-blue-100 mb-8">
            I'm always open to discussing new projects, innovative ideas, or opportunities to be part of your visions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200"
            >
              Get In Touch
            </a>
            <a
              href="mailto:<EMAIL>"
              className="bg-blue-700 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-800 transition-colors duration-200"
            >
              Send Email
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
