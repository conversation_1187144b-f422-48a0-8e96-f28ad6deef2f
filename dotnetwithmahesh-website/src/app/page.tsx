import Link from 'next/link';
import { ArrowRight, Code, Cloud, Smartphone, Database, GitBranch, Mail } from 'lucide-react';

// Mock data for featured posts - in a real app, this would come from your CMS or API
const featuredPosts = [
  {
    id: 1,
    title: "Getting Started with .NET MAUI: A Complete Guide",
    excerpt: "Learn how to build cross-platform mobile applications using .NET MAUI with this comprehensive guide covering setup, UI design, and deployment.",
    date: "2024-01-15",
    category: "MAUI Basics",
    readTime: "8 min read",
    slug: "getting-started-with-dotnet-maui"
  },
  {
    id: 2,
    title: "MAUI CollectionView: Advanced Data Display",
    excerpt: "Master the CollectionView control in .NET MAUI for displaying lists and grids with custom templates, grouping, and selection.",
    date: "2024-01-12",
    category: "UI & Controls",
    readTime: "12 min read",
    slug: "maui-collectionview-advanced-data-display"
  },
  {
    id: 3,
    title: "Data Binding Patterns in .NET MAUI",
    excerpt: "Explore different data binding techniques in MAUI including one-way, two-way binding, and value converters for dynamic UIs.",
    date: "2024-01-10",
    category: "Data Binding",
    readTime: "10 min read",
    slug: "data-binding-patterns-dotnet-maui"
  },
  {
    id: 4,
    title: "Shell Navigation in .NET MAUI Apps",
    excerpt: "Implement sophisticated navigation patterns using MAUI Shell including tabs, flyouts, and programmatic navigation.",
    date: "2024-01-08",
    category: "Navigation",
    readTime: "15 min read",
    slug: "shell-navigation-dotnet-maui-apps"
  },
  {
    id: 5,
    title: "MVVM Pattern Implementation in MAUI",
    excerpt: "Build maintainable MAUI applications using the MVVM pattern with CommunityToolkit.Mvvm and proper separation of concerns.",
    date: "2024-01-05",
    category: "MAUI Basics",
    readTime: "9 min read",
    slug: "mvvm-pattern-implementation-maui"
  },
  {
    id: 6,
    title: "Optimizing MAUI App Performance",
    excerpt: "Learn essential techniques to improve your .NET MAUI app performance including startup time, memory usage, and UI responsiveness.",
    date: "2024-01-03",
    category: "Performance",
    readTime: "7 min read",
    slug: "optimizing-maui-app-performance"
  }
];

const technologies = [
  { name: '.NET MAUI', icon: Smartphone, description: 'Cross-platform mobile framework' },
  { name: 'C# & XAML', icon: Code, description: 'Modern UI development' },
  { name: 'MVVM', icon: GitBranch, description: 'Model-View-ViewModel pattern' },
  { name: 'Data Binding', icon: Database, description: 'Dynamic UI updates' },
  { name: 'Platform APIs', icon: Cloud, description: 'Native platform integration' },
];

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Master{' '}
              <span className="text-purple-600">.NET MAUI</span>{' '}
              Development with{' '}
              <span className="text-purple-600">Daily Tutorials</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Learn to build amazing cross-platform mobile applications with .NET MAUI.
              Get practical tutorials, tips, and real-world insights from an experienced MAUI developer.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/blog" className="btn-primary inline-flex items-center">
                Read Latest Posts
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <a
                href="https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-secondary inline-flex items-center"
              >
                Connect on LinkedIn
                <ArrowRight className="ml-2 h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* About Me Preview */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">About Mahesh</h2>
              <p className="text-lg text-gray-600 mb-4">
                I'm a passionate .NET MAUI developer with over 8 years of experience building cross-platform mobile applications.
                My journey started with Xamarin.Forms and evolved into mastering .NET MAUI, helping teams build amazing
                mobile experiences across iOS, Android, Windows, and macOS.
              </p>
              <p className="text-lg text-gray-600 mb-6">
                Through this blog, I share daily MAUI tutorials, best practices, and real-world solutions to help fellow
                developers master cross-platform mobile development with .NET MAUI.
              </p>
              <Link href="/about" className="btn-primary inline-flex items-center">
                Learn More About Me
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
            <div className="flex justify-center">
              <div className="w-64 h-64 bg-gray-200 rounded-full flex items-center justify-center">
                <span className="text-gray-500 text-sm">Professional Photo</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Blog Posts */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Latest Blog Posts</h2>
            <p className="text-lg text-gray-600">
              Discover the latest insights and tutorials on .NET development and cloud technologies
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredPosts.map((post) => (
              <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Featured Image</span>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-600">{post.category}</span>
                    <span className="text-sm text-gray-500">{post.readTime}</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {new Date(post.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                    <Link
                      href={`/blog/${post.slug}`}
                      className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                    >
                      Read More →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
          <div className="text-center mt-12">
            <Link href="/blog" className="btn-primary inline-flex items-center">
              View All Posts
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Technology Stack Showcase */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Technology Stack</h2>
            <p className="text-lg text-gray-600">
              Technologies and platforms I work with and write about
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
            {technologies.map((tech) => {
              const Icon = tech.icon;
              return (
                <div key={tech.name} className="text-center">
                  <div className="bg-blue-50 rounded-lg p-6 mb-4 card-hover">
                    <Icon className="tech-icon" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">{tech.name}</h3>
                  <p className="text-sm text-gray-600">{tech.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Mail className="w-12 h-12 text-white mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-white mb-4">Stay Updated</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Get weekly .NET MAUI tutorials, tips, and best practices for cross-platform mobile development
              delivered straight to your inbox.
            </p>
            <div className="max-w-md mx-auto">
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200">
                  Subscribe
                </button>
              </div>
              <p className="text-blue-100 text-sm mt-4">
                No spam, unsubscribe at any time. Read our{' '}
                <Link href="/privacy" className="underline hover:text-white">
                  privacy policy
                </Link>
                .
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
