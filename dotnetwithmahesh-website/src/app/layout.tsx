import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "MAUI with Mahesh - .NET MAUI Development & Tutorials",
    template: "%s | MAUI with <PERSON><PERSON><PERSON>"
  },
  description: "Master .NET MAUI development with daily tutorials, tips, and insights. Learn cross-platform mobile app development with C# and XAML from real-world experience.",
  keywords: [".NET MAUI", "MAUI", "Cross-platform", "Mobile Development", "C#", "XAML", "iOS", "Android", "Windows", "macOS"],
  authors: [{ name: "<PERSON><PERSON><PERSON>have" }],
  creator: "<PERSON><PERSON>h Gadhave",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://dotnetwithmahesh.dev",
    siteName: "MAUI with <PERSON><PERSON><PERSON>",
    title: "MAUI with Mahesh - .NET MAUI Development & Tutorials",
    description: "Master .NET MAUI development with daily tutorials, tips, and insights",
  },
  twitter: {
    card: "summary_large_image",
    title: "MAUI with Mahesh",
    description: "Master .NET MAUI development with daily tutorials, tips, and insights",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased bg-gray-50 text-gray-900`}>
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
