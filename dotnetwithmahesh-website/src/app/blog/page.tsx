'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Filter, Calendar, Clock, Tag } from 'lucide-react';

// Mock blog posts data - in a real app, this would come from your CMS or API
const allPosts = [
  {
    id: 1,
    title: "Getting Started with .NET MAUI: A Complete Guide",
    excerpt: "Learn how to build cross-platform mobile applications using .NET MAUI with this comprehensive guide covering setup, UI design, and deployment.",
    date: "2024-01-15",
    category: "maui-basics",
    categoryLabel: "MAUI Basics",
    readTime: "8 min read",
    slug: "getting-started-with-dotnet-maui",
    tags: ["MAUI", "Basics", "Setup"]
  },
  {
    id: 2,
    title: "MAUI CollectionView: Advanced Data Display",
    excerpt: "Master the CollectionView control in .NET MAUI for displaying lists and grids with custom templates, grouping, and selection.",
    date: "2024-01-12",
    category: "ui-controls",
    categoryLabel: "UI & Controls",
    readTime: "12 min read",
    slug: "maui-collectionview-advanced-data-display",
    tags: ["MAUI", "CollectionView", "UI"]
  },
  {
    id: 3,
    title: "Data Binding Patterns in .NET MAUI",
    excerpt: "Explore different data binding techniques in MAUI including one-way, two-way binding, and value converters for dynamic UIs.",
    date: "2024-01-10",
    category: "data-binding",
    categoryLabel: "Data Binding",
    readTime: "10 min read",
    slug: "data-binding-patterns-dotnet-maui",
    tags: ["MAUI", "Data Binding", "MVVM"]
  },
  {
    id: 4,
    title: "Shell Navigation in .NET MAUI Apps",
    excerpt: "Implement sophisticated navigation patterns using MAUI Shell including tabs, flyouts, and programmatic navigation.",
    date: "2024-01-08",
    category: "navigation",
    categoryLabel: "Navigation",
    readTime: "15 min read",
    slug: "shell-navigation-dotnet-maui-apps",
    tags: ["MAUI", "Shell", "Navigation"]
  },
  {
    id: 5,
    title: "MVVM Pattern Implementation in MAUI",
    excerpt: "Build maintainable MAUI applications using the MVVM pattern with CommunityToolkit.Mvvm and proper separation of concerns.",
    date: "2024-01-05",
    category: "maui-basics",
    categoryLabel: "MAUI Basics",
    readTime: "9 min read",
    slug: "mvvm-pattern-implementation-maui",
    tags: ["MAUI", "MVVM", "Architecture"]
  },
  {
    id: 6,
    title: "Optimizing MAUI App Performance",
    excerpt: "Learn essential techniques to improve your .NET MAUI app performance including startup time, memory usage, and UI responsiveness.",
    date: "2024-01-03",
    category: "performance",
    categoryLabel: "Performance",
    readTime: "7 min read",
    slug: "optimizing-maui-app-performance",
    tags: ["MAUI", "Performance", "Optimization"]
  },
  {
    id: 7,
    title: "Custom Controls in .NET MAUI",
    excerpt: "Create reusable custom controls in .NET MAUI with proper templating, bindable properties, and platform-specific implementations.",
    date: "2024-01-01",
    category: "ui-controls",
    categoryLabel: "UI & Controls",
    readTime: "11 min read",
    slug: "custom-controls-dotnet-maui",
    tags: ["MAUI", "Custom Controls", "UI"]
  },
  {
    id: 8,
    title: "MAUI Handlers: Platform-Specific Customization",
    excerpt: "Deep dive into MAUI Handlers for creating platform-specific customizations and accessing native platform APIs.",
    date: "2023-12-28",
    category: "maui-basics",
    categoryLabel: "MAUI Basics",
    readTime: "8 min read",
    slug: "maui-handlers-platform-customization",
    tags: ["MAUI", "Handlers", "Platform"]
  }
];

const categories = [
  { id: 'all', label: 'All Posts', count: allPosts.length },
  { id: 'maui-basics', label: 'MAUI Basics', count: allPosts.filter(p => p.category === 'maui-basics').length },
  { id: 'ui-controls', label: 'UI & Controls', count: allPosts.filter(p => p.category === 'ui-controls').length },
  { id: 'data-binding', label: 'Data Binding', count: allPosts.filter(p => p.category === 'data-binding').length },
  { id: 'navigation', label: 'Navigation', count: allPosts.filter(p => p.category === 'navigation').length },
  { id: 'performance', label: 'Performance', count: allPosts.filter(p => p.category === 'performance').length },
];

export default function BlogPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  // Filter and sort posts
  const filteredPosts = allPosts
    .filter(post => {
      const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      if (sortBy === 'newest') {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      } else {
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      }
    });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              .NET MAUI Tutorials
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Master cross-platform mobile development with comprehensive .NET MAUI tutorials,
              tips, and best practices from real-world experience.
            </p>
          </div>
        </div>
      </section>

      {/* Filters and Search */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Sort */}
            <div className="flex items-center gap-4">
              <label className="text-sm font-medium text-gray-700">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Categories */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
              <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                <Filter className="w-5 h-5 mr-2" />
                Categories
              </h3>
              <ul className="space-y-2">
                {categories.map((category) => (
                  <li key={category.id}>
                    <button
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 flex items-center justify-between ${
                        selectedCategory === category.id
                          ? 'bg-blue-100 text-blue-700 font-medium'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <span>{category.label}</span>
                      <span className="text-sm bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                        {category.count}
                      </span>
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Main Content - Blog Posts */}
          <div className="lg:col-span-3">
            <div className="mb-6">
              <p className="text-gray-600">
                Showing {filteredPosts.length} of {allPosts.length} posts
                {selectedCategory !== 'all' && ` in ${categories.find(c => c.id === selectedCategory)?.label}`}
                {searchTerm && ` matching "${searchTerm}"`}
              </p>
            </div>

            <div className="space-y-8">
              {filteredPosts.map((post) => (
                <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                  <div className="p-6">
                    <div className="flex items-center gap-4 mb-3">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        <Tag className="w-3 h-3 mr-1" />
                        {post.categoryLabel}
                      </span>
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="w-4 h-4 mr-1" />
                        {new Date(post.date).toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="w-4 h-4 mr-1" />
                        {post.readTime}
                      </div>
                    </div>
                    
                    <h2 className="text-2xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors duration-200">
                      <Link href={`/blog/${post.slug}`}>
                        {post.title}
                      </Link>
                    </h2>
                    
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-2">
                        {post.tags.map((tag) => (
                          <span key={tag} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                      <Link 
                        href={`/blog/${post.slug}`}
                        className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                      >
                        Read More →
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>

            {filteredPosts.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No posts found matching your criteria.</p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('all');
                  }}
                  className="mt-4 text-blue-600 hover:text-blue-800 font-medium"
                >
                  Clear filters
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
