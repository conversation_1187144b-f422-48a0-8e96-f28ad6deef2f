import { Metadata } from 'next';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { Calendar, Clock, Tag, ArrowLeft, ArrowRight, Share2, Linkedin, Twitter, Facebook } from 'lucide-react';

// Mock blog post data - in a real app, this would come from your CMS or API
const blogPosts = {
  'getting-started-with-dotnet-maui': {
    id: 1,
    title: "Getting Started with .NET MAUI: A Complete Guide",
    excerpt: "Learn how to build cross-platform mobile applications using .NET MAUI with this comprehensive guide covering setup, UI design, and deployment.",
    content: `
# Getting Started with .NET MAUI: A Complete Guide

.NET Multi-platform App UI (.NET MAUI) is the evolution of Xamarin.Forms, extending it from mobile to desktop scenarios, with UI controls rebuilt from the ground up for performance and extensibility.

## What is .NET MAUI?

.NET MAUI is a cross-platform framework for creating native mobile and desktop apps with C# and XAML. Using .NET MAUI, you can develop apps that can run on Android, iOS, macOS, and Windows from a single shared code-base.

## Prerequisites

Before we start, make sure you have the following installed:

- Visual Studio 2022 17.3 or later with the .NET MAUI workload
- .NET 6 or later
- Android SDK (for Android development)
- Xcode (for iOS/macOS development, Mac only)

## Creating Your First .NET MAUI Project

Let's create a simple "Hello World" application:

\`\`\`bash
dotnet new maui -n MyFirstMauiApp
cd MyFirstMauiApp
\`\`\`

This creates a new .NET MAUI project with the following structure:

\`\`\`
MyFirstMauiApp/
├── Platforms/
│   ├── Android/
│   ├── iOS/
│   ├── MacCatalyst/
│   └── Windows/
├── Resources/
├── MainPage.xaml
├── MainPage.xaml.cs
├── App.xaml
├── App.xaml.cs
├── AppShell.xaml
├── AppShell.xaml.cs
└── MauiProgram.cs
\`\`\`

## Understanding the Project Structure

### MauiProgram.cs
This is the entry point of your application where you configure services and dependencies:

\`\`\`csharp
public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
            });

        return builder.Build();
    }
}
\`\`\`

### App.xaml and App.xaml.cs
These files define the application-level resources and handle application lifecycle events.

### AppShell.xaml
This defines the visual hierarchy of your application and handles navigation.

## Building Your First Page

Let's modify the MainPage.xaml to create a simple counter app:

\`\`\`xml
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="MyFirstMauiApp.MainPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <ScrollView>
        <VerticalStackLayout Spacing="25" Padding="30,0" VerticalOptions="Center">
            
            <Image Source="dotnet_bot.png"
                   SemanticProperties.Description="Cute dot net bot waving hi to you!"
                   HeightRequest="200"
                   HorizontalOptions="Center" />
            
            <Label x:Name="CounterLabel"
                   Text="Current count: 0"
                   SemanticProperties.HeadingLevel="Level1"
                   FontSize="18"
                   HorizontalOptions="Center" />
            
            <Button x:Name="CounterBtn"
                    Text="Click me"
                    SemanticProperties.Hint="Counts the number of times you click"
                    Clicked="OnCounterClicked"
                    HorizontalOptions="Center" />

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
\`\`\`

And the corresponding code-behind in MainPage.xaml.cs:

\`\`\`csharp
namespace MyFirstMauiApp;

public partial class MainPage : ContentPage
{
    int count = 0;

    public MainPage()
    {
        InitializeComponent();
    }

    private void OnCounterClicked(object sender, EventArgs e)
    {
        count++;

        if (count == 1)
            CounterBtn.Text = $"Clicked {count} time";
        else
            CounterBtn.Text = $"Clicked {count} times";

        CounterLabel.Text = $"Current count: {count}";

        SemanticScreenReader.Announce(CounterLabel.Text);
    }
}
\`\`\`

## Running Your Application

To run your application on different platforms:

### Android
\`\`\`bash
dotnet build -t:Run -f net6.0-android
\`\`\`

### iOS (Mac only)
\`\`\`bash
dotnet build -t:Run -f net6.0-ios
\`\`\`

### Windows
\`\`\`bash
dotnet build -t:Run -f net6.0-windows10.0.19041.0
\`\`\`

## Key Concepts to Remember

1. **Single Project**: .NET MAUI uses a single project structure that can target multiple platforms
2. **Hot Reload**: You can make changes to your XAML and see them immediately without rebuilding
3. **Platform-Specific Code**: Use the Platforms folder for platform-specific implementations
4. **Dependency Injection**: Built-in support for dependency injection through MauiProgram.cs

## Next Steps

Now that you have a basic .NET MAUI application running, you can explore:

- MVVM pattern implementation
- Data binding
- Navigation between pages
- Platform-specific features
- Custom controls and renderers

## Conclusion

.NET MAUI provides a powerful framework for building cross-platform applications with a single codebase. This guide covered the basics to get you started, but there's much more to explore as you build more complex applications.

In the next post, we'll dive deeper into MVVM patterns and data binding in .NET MAUI applications.
    `,
    date: "2024-01-15",
    category: "dotnet-maui",
    categoryLabel: ".NET MAUI",
    readTime: "8 min read",
    tags: ["MAUI", "Mobile", "Cross-platform", "Tutorial"],
    author: "Mahesh Gadhave"
  }
  // Add more blog posts here...
};

// Mock related posts
const relatedPosts = [
  {
    id: 2,
    title: "Migrating from Xamarin.Forms to .NET MAUI",
    slug: "migrating-xamarin-forms-to-dotnet-maui",
    category: "Migration"
  },
  {
    id: 5,
    title: "State Management in .NET MAUI Applications",
    slug: "state-management-dotnet-maui",
    category: ".NET MAUI"
  },
  {
    id: 7,
    title: "Implementing Authentication in .NET MAUI",
    slug: "authentication-dotnet-maui",
    category: ".NET MAUI"
  }
];

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = blogPosts[params.slug as keyof typeof blogPosts];
  
  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  return {
    title: post.title,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: 'article',
      publishedTime: post.date,
      authors: [post.author],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt,
    },
  };
}

export default function BlogPost({ params }: BlogPostPageProps) {
  const post = blogPosts[params.slug as keyof typeof blogPosts];

  if (!post) {
    notFound();
  }

  const shareUrl = `https://dotnetwithmahesh.dev/blog/${params.slug}`;
  const shareText = `Check out this article: ${post.title}`;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Article Header */}
      <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Featured Image Placeholder */}
          <div className="h-64 md:h-80 bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-2xl md:text-4xl font-bold mb-4 px-6">{post.title}</h1>
              <div className="flex items-center justify-center gap-6 text-blue-100">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  {new Date(post.date).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2" />
                  {post.readTime}
                </div>
                <div className="flex items-center">
                  <Tag className="w-4 h-4 mr-2" />
                  {post.categoryLabel}
                </div>
              </div>
            </div>
          </div>

          {/* Article Content */}
          <div className="p-8">
            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-6">
              {post.tags.map((tag) => (
                <span key={tag} className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                  {tag}
                </span>
              ))}
            </div>

            {/* Content */}
            <div className="prose prose-lg max-w-none">
              <div dangerouslySetInnerHTML={{ __html: post.content.replace(/\n/g, '<br />') }} />
            </div>

            {/* Share Buttons */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Share2 className="w-5 h-5 mr-2" />
                Share this article
              </h3>
              <div className="flex gap-4">
                <a
                  href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                  <Linkedin className="w-4 h-4" />
                  LinkedIn
                </a>
                <a
                  href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 bg-sky-500 text-white px-4 py-2 rounded-lg hover:bg-sky-600 transition-colors duration-200"
                >
                  <Twitter className="w-4 h-4" />
                  Twitter
                </a>
                <a
                  href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 bg-blue-800 text-white px-4 py-2 rounded-lg hover:bg-blue-900 transition-colors duration-200"
                >
                  <Facebook className="w-4 h-4" />
                  Facebook
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Link href="/blog" className="flex items-center text-blue-600 hover:text-blue-800 font-medium">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Blog
          </Link>
          <div className="flex gap-4">
            <button className="flex items-center text-gray-500 hover:text-gray-700">
              <ArrowLeft className="w-4 h-4 mr-1" />
              Previous
            </button>
            <button className="flex items-center text-gray-500 hover:text-gray-700">
              Next
              <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>

        {/* Related Posts */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Related Posts</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {relatedPosts.map((relatedPost) => (
              <Link key={relatedPost.id} href={`/blog/${relatedPost.slug}`}>
                <div className="bg-white rounded-lg shadow-md p-6 card-hover">
                  <span className="text-sm font-medium text-blue-600 mb-2 block">{relatedPost.category}</span>
                  <h3 className="font-semibold text-gray-900 hover:text-blue-600 transition-colors duration-200">
                    {relatedPost.title}
                  </h3>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </article>
    </div>
  );
}
