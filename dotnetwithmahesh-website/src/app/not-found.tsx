import Link from 'next/link';
import { Home, Search, ArrowLeft, BookOpen, Code, Mail } from 'lucide-react';

export default function NotFound() {
  const popularPosts = [
    {
      title: "Getting Started with .NET MAUI",
      href: "/blog/getting-started-with-dotnet-maui",
      category: ".NET MAUI"
    },
    {
      title: "Migrating from Xamarin.Forms to .NET MAUI",
      href: "/blog/migrating-xamarin-forms-to-dotnet-maui",
      category: "Migration"
    },
    {
      title: "Building Scalable APIs with Azure Functions",
      href: "/blog/building-scalable-apis-azure-functions",
      category: "Azure"
    }
  ];

  const quickLinks = [
    { name: 'Home', href: '/', icon: Home },
    { name: 'Blog', href: '/blog', icon: BookOpen },
    { name: 'Projects', href: '/projects', icon: Code },
    { name: 'Contact', href: '/contact', icon: Mail },
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-blue-600 mb-4">404</div>
          <div className="w-32 h-1 bg-blue-600 mx-auto mb-8"></div>
        </div>

        {/* Error Message */}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Page Not Found
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Oops! The page you're looking for doesn't exist. It might have been moved, 
          deleted, or you entered the wrong URL.
        </p>

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Link 
            href="/"
            className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Go Back Home
          </Link>
          <Link 
            href="/blog"
            className="inline-flex items-center bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200"
          >
            <Search className="w-5 h-5 mr-2" />
            Browse Blog Posts
          </Link>
        </div>

        {/* Search */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Search for Content</h2>
          <div className="max-w-md mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search blog posts..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    const searchTerm = (e.target as HTMLInputElement).value;
                    window.location.href = `/blog?search=${encodeURIComponent(searchTerm)}`;
                  }
                }}
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Popular Posts */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <BookOpen className="w-5 h-5 mr-2 text-blue-600" />
              Popular Blog Posts
            </h2>
            <div className="space-y-4">
              {popularPosts.map((post) => (
                <Link 
                  key={post.href}
                  href={post.href}
                  className="block p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="text-sm text-blue-600 font-medium mb-1">{post.category}</div>
                  <div className="font-semibold text-gray-900 hover:text-blue-600">
                    {post.title}
                  </div>
                </Link>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <Link 
                href="/blog"
                className="text-blue-600 hover:text-blue-800 font-medium text-sm"
              >
                View all blog posts →
              </Link>
            </div>
          </div>

          {/* Quick Navigation */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <Home className="w-5 h-5 mr-2 text-blue-600" />
              Quick Navigation
            </h2>
            <div className="space-y-3">
              {quickLinks.map((link) => {
                const Icon = link.icon;
                return (
                  <Link 
                    key={link.href}
                    href={link.href}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group"
                  >
                    <Icon className="w-5 h-5 mr-3 text-gray-400 group-hover:text-blue-600" />
                    <span className="font-medium text-gray-900 group-hover:text-blue-600">
                      {link.name}
                    </span>
                  </Link>
                );
              })}
            </div>
            <div className="mt-6 pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-600 mb-3">
                Need help finding something specific?
              </p>
              <Link 
                href="/contact"
                className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm"
              >
                <Mail className="w-4 h-4 mr-1" />
                Contact me
              </Link>
            </div>
          </div>
        </div>

        {/* Help Text */}
        <div className="mt-12 p-6 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Still can't find what you're looking for?</h3>
          <p className="text-gray-600 mb-4">
            If you believe this is an error or if you were expecting to find content at this URL, 
            please let me know so I can fix it.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link 
              href="/contact"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
            >
              Report this issue
            </Link>
            <span className="hidden sm:inline text-gray-400">•</span>
            <Link 
              href="/blog"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
            >
              Browse all content
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
