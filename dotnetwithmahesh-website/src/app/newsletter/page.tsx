'use client';

import { useState } from 'react';
import { Metada<PERSON> } from 'next';
import { Mail, CheckCircle, AlertCircle, Calendar, Users, TrendingUp, BookOpen } from 'lucide-react';

export default function NewsletterPage() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate newsletter subscription
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setEmail('');
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const benefits = [
    {
      icon: BookOpen,
      title: "Weekly Insights",
      description: "Get curated content on .NET MAUI, Xamarin, and cloud development delivered to your inbox every week."
    },
    {
      icon: TrendingUp,
      title: "Latest Trends",
      description: "Stay updated with the latest trends, tools, and best practices in the .NET ecosystem and mobile development."
    },
    {
      icon: Calendar,
      title: "Exclusive Content",
      description: "Access to subscriber-only content including detailed tutorials, code samples, and early access to new posts."
    },
    {
      icon: Users,
      title: "Community Access",
      description: "Join a community of like-minded developers and get priority support for your questions and challenges."
    }
  ];

  const recentNewsletters = [
    {
      id: 1,
      title: "Week 3: .NET MAUI Performance Optimization",
      date: "January 15, 2024",
      topics: ["Performance Tips", "Memory Management", "UI Optimization"],
      excerpt: "This week we dive deep into optimizing .NET MAUI applications for better performance and user experience."
    },
    {
      id: 2,
      title: "Week 2: Azure Functions Best Practices",
      date: "January 8, 2024",
      topics: ["Serverless", "Cost Optimization", "Monitoring"],
      excerpt: "Learn how to build efficient and cost-effective serverless solutions with Azure Functions."
    },
    {
      id: 3,
      title: "Week 1: 2024 .NET Development Roadmap",
      date: "January 1, 2024",
      topics: [".NET 8", "MAUI Updates", "Cloud Trends"],
      excerpt: "A comprehensive look at what's coming in .NET development for 2024 and how to prepare."
    }
  ];

  const stats = [
    { label: "Subscribers", value: "2,500+" },
    { label: "Weekly Issues", value: "52" },
    { label: "Open Rate", value: "68%" },
    { label: "Countries", value: "45+" }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Mail className="w-16 h-16 text-blue-600 mx-auto mb-6" />
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              DotNet Weekly
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Weekly insights on .NET development, Azure cloud services, and mobile app development 
              delivered straight to your inbox every Monday.
            </p>
            
            {/* Newsletter Signup Form */}
            <div className="max-w-md mx-auto">
              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <p className="text-green-800">Welcome aboard! Check your email to confirm your subscription.</p>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
                  <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                  <p className="text-red-800">Sorry, there was an error. Please try again.</p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Subscribing...
                    </div>
                  ) : (
                    'Subscribe'
                  )}
                </button>
              </form>
              
              <p className="text-blue-600 text-sm mt-4">
                No spam, unsubscribe at any time. Read our{' '}
                <a href="/privacy" className="underline hover:text-blue-800">
                  privacy policy
                </a>
                .
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat) => (
              <div key={stat.label} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Subscribe?</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Join thousands of developers who rely on DotNet Weekly for the latest insights, 
              tutorials, and best practices in .NET development.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit) => {
              const Icon = benefit.icon;
              return (
                <div key={benefit.title} className="bg-white p-6 rounded-lg shadow-md text-center">
                  <Icon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Recent Newsletters */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Recent Issues</h2>
            <p className="text-lg text-gray-600">
              Take a look at what subscribers have been reading lately
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {recentNewsletters.map((newsletter) => (
              <div key={newsletter.id} className="bg-gray-50 rounded-lg p-6">
                <div className="text-sm text-blue-600 font-medium mb-2">{newsletter.date}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{newsletter.title}</h3>
                <p className="text-gray-600 mb-4">{newsletter.excerpt}</p>
                <div className="flex flex-wrap gap-2">
                  {newsletter.topics.map((topic) => (
                    <span key={topic} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                      {topic}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* What to Expect */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">What to Expect</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-white">
            <div>
              <h3 className="text-xl font-bold mb-3">Monday Delivery</h3>
              <p className="text-blue-100">
                Every Monday morning, get a curated newsletter with the week's most important 
                .NET and cloud development insights.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-3">Quality Content</h3>
              <p className="text-blue-100">
                No fluff, just actionable insights, code examples, and practical tips you can 
                apply to your projects immediately.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-3">Community Focus</h3>
              <p className="text-blue-100">
                Join a community of developers sharing experiences, asking questions, and 
                growing together in the .NET ecosystem.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">What Subscribers Say</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <p className="text-gray-600 mb-4">
                "DotNet Weekly has become my go-to resource for staying updated with .NET MAUI. 
                The insights are practical and immediately applicable to my projects."
              </p>
              <div className="font-semibold text-gray-900">Sarah Chen</div>
              <div className="text-sm text-gray-500">Senior Mobile Developer</div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <p className="text-gray-600 mb-4">
                "The migration guides from Xamarin to .NET MAUI were incredibly helpful. 
                Saved our team weeks of research and trial-and-error."
              </p>
              <div className="font-semibold text-gray-900">Michael Rodriguez</div>
              <div className="text-sm text-gray-500">Tech Lead</div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <p className="text-gray-600 mb-4">
                "Love the cloud architecture insights! The Azure and AWS tips have helped 
                optimize our infrastructure costs significantly."
              </p>
              <div className="font-semibold text-gray-900">David Kim</div>
              <div className="text-sm text-gray-500">DevOps Engineer</div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Ready to Level Up Your .NET Skills?</h2>
          <p className="text-xl text-gray-600 mb-8">
            Join 2,500+ developers who trust DotNet Weekly for their professional development.
          </p>
          <div className="max-w-md mx-auto">
            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                required
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                Subscribe Now
              </button>
            </form>
          </div>
        </div>
      </section>
    </div>
  );
}
