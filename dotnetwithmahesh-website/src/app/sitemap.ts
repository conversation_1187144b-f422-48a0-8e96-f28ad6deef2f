import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://dotnetwithmahesh.dev'
  
  // Static pages
  const staticPages = [
    '',
    '/about',
    '/blog',
    '/projects',
    '/contact',
    '/newsletter',
  ]

  // Blog posts (in a real app, you'd fetch these from your CMS)
  const blogPosts = [
    'getting-started-with-dotnet-maui',
    'migrating-xamarin-forms-to-dotnet-maui',
    'building-scalable-apis-azure-functions',
    'cicd-pipeline-dotnet-maui-apps',
    'state-management-dotnet-maui',
    'aws-lambda-dotnet-8-performance',
    'authentication-dotnet-maui',
    'monitoring-dotnet-application-insights',
  ]

  const staticSitemap = staticPages.map((page) => ({
    url: `${baseUrl}${page}`,
    lastModified: new Date(),
    changeFrequency: page === '' ? 'daily' as const : 'weekly' as const,
    priority: page === '' ? 1 : 0.8,
  }))

  const blogSitemap = blogPosts.map((slug) => ({
    url: `${baseUrl}/blog/${slug}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.6,
  }))

  return [...staticSitemap, ...blogSitemap]
}
