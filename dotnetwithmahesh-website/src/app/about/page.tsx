import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Calendar, MapPin, Mail, Linkedin, Github } from 'lucide-react';

export const metadata: Metadata = {
  title: 'About <PERSON><PERSON>h Gadhave',
  description: 'Learn about <PERSON><PERSON><PERSON>\'s journey as a .NET MAUI developer and his mission to share daily MAUI tutorials and insights with the developer community.',
};

const timelineEvents = [
  {
    year: '2016',
    title: 'Started with Xamarin.Forms',
    description: 'Began my cross-platform mobile development journey with Xamarin.Forms, building apps for iOS and Android with shared C# code.'
  },
  {
    year: '2018',
    title: 'Advanced Xamarin Development',
    description: 'Mastered advanced Xamarin.Forms concepts including custom renderers, effects, and platform-specific implementations.'
  },
  {
    year: '2020',
    title: 'MVVM & Architecture Patterns',
    description: 'Specialized in MVVM pattern, dependency injection, and clean architecture principles for maintainable mobile applications.'
  },
  {
    year: '2022',
    title: '.NET MAUI Early Adoption',
    description: 'Became an early adopter of .NET MAUI, leading migration projects and exploring the new framework capabilities.'
  },
  {
    year: '2024',
    title: 'MAUI Mastery & Teaching',
    description: 'Started this blog to share daily .NET MAUI tutorials and help developers master cross-platform mobile development.'
  }
];

const skills = [
  { category: 'MAUI Development', items: ['.NET MAUI', 'XAML', 'C#', 'MVVM', 'Data Binding'] },
  { category: 'UI & Controls', items: ['CollectionView', 'Shell Navigation', 'Custom Controls', 'Handlers', 'Layouts'] },
  { category: 'Architecture', items: ['MVVM Pattern', 'Dependency Injection', 'CommunityToolkit.Mvvm', 'Clean Architecture'] },
  { category: 'Platform Integration', items: ['Platform APIs', 'Native Features', 'Performance Optimization', 'Testing'] }
];

export default function About() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Hi, I'm <span className="text-blue-600">Mahesh Gadhave</span>
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                A passionate .NET MAUI developer with 8+ years of experience building cross-platform mobile applications.
                I specialize in .NET MAUI development, XAML UI design, and mobile app architecture.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/contact" className="btn-primary inline-flex items-center">
                  Get In Touch
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <a 
                  href="/resume.pdf" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="btn-secondary inline-flex items-center"
                >
                  Download Resume
                  <ArrowRight className="ml-2 h-5 w-5" />
                </a>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="w-80 h-80 bg-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-500">Professional Photo</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Journey */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">My Professional Journey</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              From building my first Xamarin app to architecting cloud-native solutions, 
              here's how my career has evolved over the years.
            </p>
          </div>
          
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gray-300"></div>
            <div className="space-y-12">
              {timelineEvents.map((event, index) => (
                <div key={event.year} className={`relative flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                  <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white p-6 rounded-lg shadow-md">
                      <div className="text-blue-600 font-bold text-lg mb-2">{event.year}</div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{event.title}</h3>
                      <p className="text-gray-600">{event.description}</p>
                    </div>
                  </div>
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">My Mission</h2>
          <p className="text-xl text-gray-600 mb-8">
            "To share daily .NET MAUI knowledge and practical tutorials that help fellow developers master
            cross-platform mobile development and build amazing applications."
          </p>
          <div className="bg-white p-8 rounded-lg shadow-md">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Why I Focus on MAUI</h3>
            <p className="text-lg text-gray-600 mb-4">
              .NET MAUI represents the future of cross-platform mobile development with .NET.
              By sharing daily tutorials and insights, I help developers transition from Xamarin.Forms
              and master this powerful framework.
            </p>
            <p className="text-lg text-gray-600">
              Each tutorial is crafted from real-world experience, whether it's implementing complex UI patterns,
              optimizing app performance, or integrating platform-specific features in .NET MAUI.
            </p>
          </div>
        </div>
      </section>

      {/* Skills & Expertise */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">MAUI Expertise</h2>
            <p className="text-lg text-gray-600">
              .NET MAUI technologies and concepts I specialize in and teach through daily tutorials
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {skills.map((skillGroup) => (
              <div key={skillGroup.category} className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-xl font-bold text-gray-900 mb-4">{skillGroup.category}</h3>
                <ul className="space-y-2">
                  {skillGroup.items.map((skill) => (
                    <li key={skill} className="text-gray-600 flex items-center">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                      {skill}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">Let's Connect</h2>
          <p className="text-xl text-blue-100 mb-8">
            I'm always interested in connecting with fellow developers, discussing new technologies, 
            or exploring collaboration opportunities.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <a 
              href="mailto:<EMAIL>"
              className="bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-lg hover:bg-opacity-20 transition-all duration-200"
            >
              <Mail className="w-8 h-8 text-white mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">Email</h3>
              <p className="text-blue-100 text-sm"><EMAIL></p>
            </a>
            <a
              href="https://www.linkedin.com/in/mahesh-gadhave-xamarin-maui-developer/"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-lg hover:bg-opacity-20 transition-all duration-200"
            >
              <Linkedin className="w-8 h-8 text-white mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">LinkedIn</h3>
              <p className="text-blue-100 text-sm">Professional Network</p>
            </a>
            <a 
              href="https://github.com/maheshgadhave"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-lg hover:bg-opacity-20 transition-all duration-200"
            >
              <Github className="w-8 h-8 text-white mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">GitHub</h3>
              <p className="text-blue-100 text-sm">Code & Projects</p>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
